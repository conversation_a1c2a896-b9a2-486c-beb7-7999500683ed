import { defineConfig } from '@hey-api/openapi-ts'

export default defineConfig({
  input: 'openapi.json',
  output: {
    format: 'prettier',
    lint: 'eslint',
    path: './src/lib/api-sdk'
  },
  plugins: [
    {
      name: '@hey-api/client-next',
      runtimeConfigPath: './src/lib/clientConfig'
    },
    {
      name: '@tanstack/react-query',
      infiniteQueryOptions: true,
      mutationOptions: true,
      queryOptions: true
    }
  ]
})
