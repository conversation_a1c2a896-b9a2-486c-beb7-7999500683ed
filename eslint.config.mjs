// @ts-expect-error - eslint-plugin-disable doesn't have TypeScript declarations
import * as disable from 'eslint-plugin-disable'
import unicorn from 'eslint-plugin-unicorn'
import { dirname } from 'path'
import { fileURLToPath } from 'url'
import { FlatCompat } from '@eslint/eslintrc'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname
})

const eslintConfig = [
  {
    rules: {
      '@typescript-eslint/ban-ts-comment': 'warn',
      '@typescript-eslint/no-empty-object-type': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          vars: 'all',
          args: 'after-used',
          ignoreRestSiblings: false,
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^(_|ignore)'
        }
      ]
    }
  },
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    plugins: {
      unicorn,
      disable
    },
    rules: {
      'unicorn/filename-case': [
        'error',
        {
          cases: {
            camelCase: true,
            pascalCase: true
          }
        }
      ],
      'unicorn/no-null': 0,
      'unicorn/no-array-reduce': 0,
      'unicorn/prefer-top-level-await': 0,
      'unicorn/prefer-global-this': 0,
      'unicorn/prevent-abbreviations': [
        'error',
        {
          allowList: {
            params: true,
            Params: true,
            env: true,
            Env: true,
            dev: true,
            Dev: true,
            prod: true,
            Prod: true,
            props: true,
            Props: true,
            ref: true,
            Ref: true
          }
        }
      ],
      'unicorn/import-style': [
        'error',
        {
          styles: {
            util: false,
            path: {
              named: true
            }
          }
        }
      ]
    }
  },
  {
    ignores: [
      'node_modules/**',
      '.next/**',
      '.pnpm-store/**',
      'src/migrations/**',
      'src/payload-types.ts',
      'openapi.json'
    ]
  }
]

export default eslintConfig
