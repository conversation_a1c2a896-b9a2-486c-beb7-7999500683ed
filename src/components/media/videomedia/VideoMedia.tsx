'use client'

import React, { useEffect, useRef } from 'react'
import type { Props as MediaProperties } from '@/components/media/types'
import { getMediaUrl } from '@/utils'
import { cn } from '@/utils'

export const VideoMedia: React.FC<MediaProperties> = (properties) => {
  const { onClick, resource, videoClassName } = properties

  const videoReference = useRef<HTMLVideoElement>(null)
  // const [showFallback] = useState<boolean>()

  useEffect(() => {
    const { current: video } = videoReference
    if (video) {
      video.addEventListener('suspend', () => {
        // setShowFallback(true);
        // console.warn('Video was suspended, rendering fallback image.')
      })
    }
  }, [])

  if (resource && typeof resource === 'object') {
    const { filename } = resource

    return (
      <video
        autoPlay
        className={cn(videoClassName)}
        controls={false}
        loop
        muted
        onClick={onClick}
        playsInline
        ref={videoReference}>
        <source src={getMediaUrl(`/media/${filename}`)} />
      </video>
    )
  }

  return null
}
