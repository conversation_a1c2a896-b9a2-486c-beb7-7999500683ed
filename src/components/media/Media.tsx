import React, { Fragment } from 'react'
import type { Props } from '@/components/media/types'
import { ImageMedia } from './imagemedia'
import { VideoMedia } from './videomedia'

export const Media: React.FC<Props> = (properties) => {
  const { className, htmlElement = 'div', resource } = properties

  const isVideo = typeof resource === 'object' && resource?.mimeType?.includes('video')
  const Tag = htmlElement || Fragment

  return (
    <Tag
      {...(htmlElement === null
        ? {}
        : {
            className
          })}>
      {isVideo ? <VideoMedia {...properties} /> : <ImageMedia {...properties} />}
    </Tag>
  )
}
