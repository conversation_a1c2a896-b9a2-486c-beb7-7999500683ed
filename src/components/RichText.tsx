import { Banner<PERSON><PERSON> } from '@/blocks/Banner'
import { CallToActionBlock } from '@/blocks/CallToAction'
import { MediaBlock } from '@/blocks/MediaBlock'
import { CodeBlock, CodeBlockProps } from '@/blocks/code/CodeBlock'
import type {
  BannerBlock as BannerBlockProperties,
  CallToActionBlock as CTABlockProperties,
  MediaBlock as MediaBlockProperties
} from '@/payload-types'
import { cn } from '@/utils'
import {
  DefaultNodeTypes,
  type DefaultTypedEditorState,
  SerializedBlockNode,
  SerializedLinkNode
} from '@payloadcms/richtext-lexical'
import {
  RichText as ConvertRichText,
  JSXConvertersFunction,
  LinkJSXConverter
} from '@payloadcms/richtext-lexical/react'

type NodeTypes =
  | DefaultNodeTypes
  | SerializedBlockNode<
      CTABlockProperties | MediaBlockProperties | BannerBlockProperties | CodeBlockProps
    >

const internalDocumentToHref = ({ linkNode }: { linkNode: SerializedLinkNode }) => {
  const { value, relationTo } = linkNode.fields.doc!
  if (typeof value !== 'object') {
    throw new TypeError('Expected value to be an object')
  }
  const slug = value.slug
  return relationTo === 'posts' ? `/posts/${slug}` : `/${slug}`
}

const jsxConverters: JSXConvertersFunction<NodeTypes> = ({ defaultConverters }) => ({
  ...defaultConverters,
  ...LinkJSXConverter({ internalDocToHref: internalDocumentToHref }),
  blocks: {
    banner: ({ node }) => <BannerBlock className='col-start-2 mb-4' {...node.fields} />,
    mediaBlock: ({ node }) => (
      <MediaBlock
        className='col-start-1 col-span-3'
        imgClassName='m-0'
        {...node.fields}
        captionClassName='mx-auto max-w-3xl'
        enableGutter={false}
        disableInnerContainer={true}
      />
    ),
    code: ({ node }) => <CodeBlock className='col-start-2' {...node.fields} />,
    cta: ({ node }) => <CallToActionBlock {...node.fields} />
  }
})

type Properties = {
  data: DefaultTypedEditorState
  enableGutter?: boolean
  enableProse?: boolean
} & React.HTMLAttributes<HTMLDivElement>

export function RichText(properties: Properties) {
  const { className, enableProse = true, enableGutter = true, ...rest } = properties
  return (
    <ConvertRichText
      converters={jsxConverters}
      className={cn(
        'payload-richtext',
        {
          container: enableGutter,
          'max-w-none': !enableGutter,
          'mx-auto prose md:prose-md dark:prose-invert': enableProse
        },
        className
      )}
      {...rest}
    />
  )
}
