import * as React from 'react'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'
import type { ButtonProps } from '@/components/ui/Button'
import { cn } from '@/utils'
import { buttonVariants } from './Button'

const Pagination = ({ className, ...properties }: React.ComponentProps<'nav'>) => (
  <nav
    aria-label='pagination'
    className={cn('mx-auto flex w-full justify-center', className)}
    role='navigation'
    {...properties}
  />
)

const PaginationContent: React.FC<
  { ref?: React.Ref<HTMLUListElement> } & React.HTMLAttributes<HTMLUListElement>
> = ({ className, ref, ...properties }) => (
  <ul
    className={cn('flex flex-row items-center gap-1', className)}
    ref={ref}
    {...properties}
  />
)

const PaginationItem: React.FC<
  { ref?: React.Ref<HTMLLIElement> } & React.HTMLAttributes<HTMLLIElement>
> = ({ className, ref, ...properties }) => (
  <li className={cn('', className)} ref={ref} {...properties} />
)

type PaginationLinkProperties = {
  isActive?: boolean
} & Pick<ButtonProps, 'size'> &
  React.ComponentProps<'button'>

const PaginationLink = ({
  className,
  isActive,
  size = 'icon',
  ...properties
}: PaginationLinkProperties) => (
  <button
    aria-current={isActive ? 'page' : undefined}
    className={cn(
      buttonVariants({
        size,
        variant: isActive ? 'outline' : 'ghost'
      }),
      className
    )}
    {...properties}
  />
)

const PaginationPrevious = ({
  className,
  ...properties
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label='Go to previous page'
    className={cn('gap-1 pl-2.5', className)}
    size='default'
    {...properties}>
    <ChevronLeft className='h-4 w-4' />
    <span>Previous</span>
  </PaginationLink>
)

const PaginationNext = ({
  className,
  ...properties
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label='Go to next page'
    className={cn('gap-1 pr-2.5', className)}
    size='default'
    {...properties}>
    <span>Next</span>
    <ChevronRight className='h-4 w-4' />
  </PaginationLink>
)

const PaginationEllipsis = ({ className, ...properties }: React.ComponentProps<'span'>) => (
  <span
    aria-hidden
    className={cn('flex h-9 w-9 items-center justify-center', className)}
    {...properties}>
    <MoreHorizontal className='h-4 w-4' />
    <span className='sr-only'>More pages</span>
  </span>
)

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
}
