import * as React from 'react'
import { cn } from '@/utils'

const Card: React.FC<
  { ref?: React.Ref<HTMLDivElement> } & React.HTMLAttributes<HTMLDivElement>
> = ({ className, ref, ...properties }) => (
  <div
    className={cn('rounded-lg border bg-card text-card-foreground shadow-xs', className)}
    ref={ref}
    {...properties}
  />
)

const CardHeader: React.FC<
  { ref?: React.Ref<HTMLDivElement> } & React.HTMLAttributes<HTMLDivElement>
> = ({ className, ref, ...properties }) => (
  <div className={cn('flex flex-col space-y-1.5 p-6', className)} ref={ref} {...properties} />
)

const CardTitle: React.FC<
  { ref?: React.Ref<HTMLHeadingElement> } & React.HTMLAttributes<HTMLHeadingElement>
> = ({ className, ref, ...properties }) => (
  <h3
    className={cn('text-2xl font-semibold leading-none tracking-tight', className)}
    ref={ref}
    {...properties}
  />
)

const CardDescription: React.FC<
  { ref?: React.Ref<HTMLParagraphElement> } & React.HTMLAttributes<HTMLParagraphElement>
> = ({ className, ref, ...properties }) => (
  <p className={cn('text-sm text-muted-foreground', className)} ref={ref} {...properties} />
)

const CardContent: React.FC<
  { ref?: React.Ref<HTMLDivElement> } & React.HTMLAttributes<HTMLDivElement>
> = ({ className, ref, ...properties }) => (
  <div className={cn('p-6 pt-0', className)} ref={ref} {...properties} />
)

const CardFooter: React.FC<
  { ref?: React.Ref<HTMLDivElement> } & React.HTMLAttributes<HTMLDivElement>
> = ({ className, ref, ...properties }) => (
  <div className={cn('flex items-center p-6 pt-0', className)} ref={ref} {...properties} />
)

export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle }
