import React from 'react'
import clsx from 'clsx'

interface Properties {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const Logo = (properties: Properties) => {
  const { loading: loadingFromProperties, priority: priorityFromProperties, className } = properties

  const loading = loadingFromProperties || 'lazy'
  const priority = priorityFromProperties || 'low'

  return (
    /* eslint-disable @next/next/no-img-element */
    <img
      alt='Payload Logo'
      width={193}
      height={34}
      loading={loading}
      fetchPriority={priority}
      decoding='async'
      className={clsx('max-w-37.5 w-full h-[34px]', className)}
      src='https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-logo-light.svg'
    />
  )
}
