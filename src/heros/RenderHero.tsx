import React from 'react'
import { HighImpactHero, LowImpactHero, MediumImpactHero } from '@/heros'
import type { Page } from '@/payload-types'

const heroes = {
  highImpact: HighImpactHero,
  lowImpact: LowImpactHero,
  mediumImpact: MediumImpactHero
}

export const RenderHero: React.FC<Page['hero']> = (properties) => {
  const { type } = properties || {}

  if (!type || type === 'none') return null

  const HeroToRender = heroes[type]

  if (!HeroToRender) return null

  return <HeroToRender {...properties} />
}
