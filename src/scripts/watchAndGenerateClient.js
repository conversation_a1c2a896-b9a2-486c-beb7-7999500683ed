import { spawn } from 'child_process'
import { existsSync, watch } from 'fs'
import path from 'path'

// Safety check: Only run in development
if (process.env.NODE_ENV !== 'development') {
  console.log('⚠️  API client watcher is only for development mode')
  process.exit(0)
}

// Safety check: Ensure we're in the right directory
if (!existsSync('package.json') || !existsSync('src')) {
  console.error('❌ API client watcher must be run from project root')
  process.exit(1)
}

let isGenerating = false
let hasGenerated = false

const generateClient = async () => {
  if (isGenerating || hasGenerated) return

  isGenerating = true

  try {
    console.log('🔧 Generating API client with Hey API and TanStack Query...')

    const clientProcess = spawn('pnpm', ['generate:api'], {
      stdio: 'inherit',
      shell: true
    })

    clientProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ API client generated successfully!')
        hasGenerated = true
      } else {
        console.log('⚠️  API client generation failed, but continuing...')
      }
      isGenerating = false
    })

    clientProcess.on('error', (error) => {
      console.log('⚠️  API client generation error:', error.message)
      isGenerating = false
    })
  } catch (error) {
    console.log('⚠️  API client generation error:', error.message)
    isGenerating = false
  }
}

const startWatcher = () => {
  const openapiPath = path.resolve('openapi.json')

  // Check if file already exists and generate immediately
  if (existsSync(openapiPath)) {
    console.log('📄 OpenAPI spec found, generating client...')
    generateClient()
    return
  }

  console.log('👀 Watching for OpenAPI spec generation...')

  // Watch for file creation/changes
  const watcher = watch('.', { persistent: false }, (eventType, filename) => {
    if (filename === 'openapi.json' && eventType === 'rename') {
      // File was created
      if (existsSync(openapiPath)) {
        console.log('📄 OpenAPI spec generated, triggering client generation...')
        generateClient()
        watcher.close()
      }
    }
  })

  // Cleanup watcher after 30 seconds if nothing happens
  setTimeout(() => {
    if (!hasGenerated) {
      console.log('⏰ Timeout waiting for OpenAPI spec, closing watcher')
      watcher.close()
    }
  }, 30000)
}

// Start watching
startWatcher()
