import * as TJS from 'typescript-json-schema'
import camelCase from 'camelcase'
import fs from 'node:fs'
import path from 'node:path'
import type { CollectionConfig } from 'payload'
import configPromise from '@/payload.config'

const ignoreList = new Set([
  'payload-locked-documents',
  'payload-preferences',
  'payload-migrations',
  'payload-jobs',
  'redirects',
  'forms',
  'form-submissions',
  'search'
])

// Define OpenAPI path item and document types
type OpenAPIPathItem = {
  get?: OpenAPIOperation
  post?: OpenAPIOperation
  patch?: OpenAPIOperation
  delete?: OpenAPIOperation
  [key: string]: OpenAPIOperation | undefined
}

type OpenAPIParameter = {
  name: string
  in: string
  required?: boolean
  schema: {
    type: string
    minimum?: number
    maximum?: number
    format?: string
  }
  description?: string
}

type OpenAPISchema = {
  type?: string
  properties?: Record<string, OpenAPISchema>
  items?: OpenAPISchema
  $ref?: string
  required?: string[]
  minimum?: number
  maximum?: number
  format?: string
  nullable?: boolean
  enum?: string[]
  description?: string
}

type OpenAPIOperation = {
  summary: string
  operationId: string
  tags: string[]
  parameters?: OpenAPIParameter[]
  requestBody?: {
    content: {
      'application/json'?: {
        schema: OpenAPISchema
      }
      'multipart/form-data'?: {
        schema: OpenAPISchema
      }
    }
  }
  responses: Record<
    string,
    {
      description: string
      content?: {
        'application/json': {
          schema: OpenAPISchema
        }
      }
    }
  >
}

// OpenAPI base document with components
const openApiDocument: {
  openapi: string
  info: {
    title: string
    version: string
    description: string
  }
  paths: Record<string, OpenAPIPathItem>
  components: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    schemas: Record<string, any>
  }
} = {
  openapi: '3.0.0',
  info: {
    title: 'SERPlens API',
    version: '1.0.0',
    description: 'Auto-generated OpenAPI document for the SERPlens API.'
  },
  paths: {},
  components: {
    schemas: {}
  }
}

const processOperationId = (name: string) => {
  return camelCase(name)
}

// TypeScript schema generation utilities
type SchemaGenerator = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getSchemaForSymbol: (symbolName: string) => any | null
}

/**
 * Create TypeScript schema generator from payload-types.ts
 */
const createSchemaGenerator = (): SchemaGenerator | null => {
  try {
    const payloadTypesPath = path.resolve(process.cwd(), 'src/payload-types.ts')

    // Check if payload-types.ts exists
    if (!fs.existsSync(payloadTypesPath)) {
      console.warn('⚠️  payload-types.ts not found. Run `pnpm generate:types` first.')
      return null
    }

    // Create TypeScript program
    const program = TJS.getProgramFromFiles([payloadTypesPath], {
      target: 7, // ES2020
      module: 1, // CommonJS
      skipLibCheck: true,
      strict: false
    })

    // Build generator with settings
    const generator = TJS.buildGenerator(program, {
      required: true,
      noExtraProps: false,
      propOrder: false,
      ignoreErrors: true,
      excludePrivate: true,
      topRef: false,
      titles: false,
      defaultProps: false
    })

    if (!generator) {
      console.warn('⚠️  Failed to create TypeScript schema generator')
      return null
    }

    return {
      getSchemaForSymbol: (symbolName: string) => {
        try {
          const schema = TJS.generateSchema(program, symbolName, {
            required: true,
            noExtraProps: false,
            propOrder: false,
            ignoreErrors: true,
            excludePrivate: true,
            topRef: false,
            titles: false,
            defaultProps: false
          })
          return schema
        } catch (error) {
          console.warn(`⚠️  Failed to generate schema for ${symbolName}:`, error)
          return null
        }
      }
    }
  } catch (error) {
    console.error('❌ Error creating schema generator:', error)
    return null
  }
}

/**
 * Map collection slug to TypeScript interface name
 */
const getTypeNameFromSlug = (slug: string): string => {
  // Static mapping for known collections
  const typeMap: Record<string, string> = {
    pages: 'Page',
    posts: 'Post',
    media: 'Media',
    categories: 'Category',
    users: 'User'
  }

  // If not in map, convert slug to PascalCase
  if (typeMap[slug]) {
    return typeMap[slug]
  }

  // Convert kebab-case or snake_case to PascalCase
  return slug
    .split(/[-_]/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('')
}

/**
 * Generate OpenAPI schema from TypeScript interface with fallback
 */
const getSchemaForCollection = (
  _generator: SchemaGenerator | null,
  slug: string
): OpenAPISchema => {
  // For now, use fallback schemas to avoid circular reference issues
  // TODO: Implement proper TypeScript schema conversion without circular refs
  console.log(`📝 Using fallback schema for collection: ${slug}`)
  return createEnhancedFallbackSchema(slug)
}

/**
 * Create enhanced fallback schema based on collection type
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createEnhancedFallbackSchema = (slug: string): any => {
  const baseSchema = {
    type: 'object',
    properties: {
      id: { type: 'number' },
      updatedAt: { type: 'string', format: 'date-time' },
      createdAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'updatedAt', 'createdAt']
  }

  // Add collection-specific properties
  switch (slug) {
    case 'pages':
      return {
        ...baseSchema,
        properties: {
          ...baseSchema.properties,
          title: { type: 'string' },
          slug: { type: 'string', nullable: true },
          publishedAt: { type: 'string', format: 'date-time', nullable: true },
          _status: { type: 'string', enum: ['draft', 'published'], nullable: true }
        },
        required: [...baseSchema.required, 'title']
      }
    case 'posts':
      return {
        ...baseSchema,
        properties: {
          ...baseSchema.properties,
          title: { type: 'string' },
          slug: { type: 'string', nullable: true },
          publishedAt: { type: 'string', format: 'date-time', nullable: true },
          _status: { type: 'string', enum: ['draft', 'published'], nullable: true }
        },
        required: [...baseSchema.required, 'title']
      }
    case 'media':
      return {
        ...baseSchema,
        properties: {
          ...baseSchema.properties,
          alt: { type: 'string', nullable: true },
          filename: { type: 'string', nullable: true },
          mimeType: { type: 'string', nullable: true },
          filesize: { type: 'number', nullable: true },
          width: { type: 'number', nullable: true },
          height: { type: 'number', nullable: true },
          url: { type: 'string', nullable: true }
        }
      }
    case 'categories':
      return {
        ...baseSchema,
        properties: {
          ...baseSchema.properties,
          title: { type: 'string' },
          slug: { type: 'string', nullable: true }
        },
        required: [...baseSchema.required, 'title']
      }
    case 'users':
      return {
        ...baseSchema,
        properties: {
          ...baseSchema.properties,
          name: { type: 'string', nullable: true },
          email: { type: 'string', format: 'email' },
          roles: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['subscriber', 'editor', 'admin', 'super-admin']
            }
          }
        },
        required: [...baseSchema.required, 'email', 'roles']
      }
    default:
      return baseSchema
  }
}

// Removed createFallbackSchema function - using createEnhancedFallbackSchema instead

// Removed cleanSchemaForOpenAPI function as it's not currently used
// TODO: Re-implement when adding proper TypeScript schema conversion

/**
 * Create paginated response schema
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createPaginatedResponseSchema = (itemSchema: any): any => ({
  type: 'object',
  properties: {
    docs: {
      type: 'array',
      items: itemSchema
    },
    totalDocs: { type: 'integer', minimum: 0 },
    limit: { type: 'integer', minimum: 1 },
    totalPages: { type: 'integer', minimum: 0 },
    page: { type: 'integer', minimum: 1 },
    pagingCounter: { type: 'integer', minimum: 1 },
    hasPrevPage: { type: 'boolean' },
    hasNextPage: { type: 'boolean' },
    prevPage: { type: 'integer', minimum: 1, nullable: true },
    nextPage: { type: 'integer', minimum: 1, nullable: true }
  },
  required: [
    'docs',
    'totalDocs',
    'limit',
    'totalPages',
    'page',
    'pagingCounter',
    'hasPrevPage',
    'hasNextPage'
  ]
})

// Function to convert Payload collections to OpenAPI paths
const convertCollectionToOpenApi = (
  collection: CollectionConfig,
  generator: SchemaGenerator | null
) => {
  const { slug, labels, upload } = collection
  // Handle potentially undefined labels with defaults
  const pluralSentence = typeof labels?.plural === 'string' ? labels.plural : slug
  const singularSentence = typeof labels?.singular === 'string' ? labels.singular : slug

  const plural = pluralSentence.toLowerCase()
  const singular = singularSentence.toLowerCase()

  const path = `/api/${slug}`

  // Generate schema for this collection
  const collectionSchema = getSchemaForCollection(generator, slug)
  const typeName = getTypeNameFromSlug(slug)

  // Add schema to components
  openApiDocument.components.schemas[typeName] = collectionSchema

  // Create reference to the schema
  const schemaRef = { $ref: `#/components/schemas/${typeName}` }

  // Create paginated response schema
  const paginatedSchema = createPaginatedResponseSchema(schemaRef)

  // Add path to openApiDocument.paths
  if (!openApiDocument.paths[path]) {
    openApiDocument.paths[path] = {}
  }

  // Find
  openApiDocument.paths[path].get = {
    summary: `List all ${plural}`,
    operationId: processOperationId(`find-${plural}`),
    tags: [slug],
    parameters: [
      {
        name: 'depth',
        in: 'query',
        required: false,
        schema: { type: 'integer', minimum: 0, maximum: 10 },
        description: 'Auto-populate relationships and uploads'
      },
      {
        name: 'limit',
        in: 'query',
        required: false,
        schema: { type: 'integer', minimum: 1, maximum: 100 },
        description: 'Limit number of documents returned'
      },
      {
        name: 'page',
        in: 'query',
        required: false,
        schema: { type: 'integer', minimum: 1 },
        description: 'Page number for pagination'
      },
      {
        name: 'sort',
        in: 'query',
        required: false,
        schema: { type: 'string' },
        description: 'Field(s) to sort by'
      },
      {
        name: 'where',
        in: 'query',
        required: false,
        schema: { type: 'string' },
        description: 'Advanced filtering (JSON string)'
      }
    ],
    responses: {
      200: {
        description: `A list of ${plural}`,
        content: {
          'application/json': {
            schema: paginatedSchema
          }
        }
      }
    }
  }

  // Create
  openApiDocument.paths[path].post = {
    summary: `Create a new ${singular}`,
    operationId: processOperationId(`create-${singular}`),
    tags: [slug],
    requestBody: {
      content: upload
        ? {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary'
                  },
                  _payload: {
                    type: 'string',
                    description: 'JSON-stringified object containing collection fields'
                  }
                },
                required: ['file']
              }
            }
          }
        : {
            'application/json': {
              schema: schemaRef
            }
          }
    },
    responses: {
      201: {
        description: `Created ${singular}`,
        content: {
          'application/json': {
            schema: schemaRef
          }
        }
      }
    }
  }

  // Update
  openApiDocument.paths[path].patch = {
    summary: `Update ${plural}`,
    operationId: processOperationId(`update-${plural}`),
    tags: [slug],
    requestBody: {
      content: upload
        ? {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary'
                  },
                  _payload: {
                    type: 'string',
                    description: 'JSON-stringified object containing collection fields'
                  }
                }
              }
            }
          }
        : {
            'application/json': {
              schema: schemaRef
            }
          }
    },
    responses: {
      200: {
        description: `Updated ${plural}`,
        content: {
          'application/json': {
            schema: schemaRef
          }
        }
      }
    }
  }

  // Delete
  openApiDocument.paths[path].delete = {
    summary: `Delete ${plural}`,
    operationId: processOperationId(`delete-${plural}`),
    tags: [slug],
    responses: {
      204: {
        description: `${plural} deleted successfully`
      }
    }
  }

  // Find By ID, Update By ID, Delete By ID
  const pathWithId = `${path}/{id}`
  openApiDocument.paths[pathWithId] = {}

  // Get by ID
  openApiDocument.paths[pathWithId].get = {
    summary: `Retrieve a ${singular} by ID`,
    operationId: processOperationId(`find-${singular}-by-id`),
    tags: [slug],
    parameters: [
      { name: 'id', in: 'path', required: true, schema: { type: 'string' } },
      {
        name: 'depth',
        in: 'query',
        required: false,
        schema: { type: 'integer', minimum: 0, maximum: 10 },
        description: 'Auto-populate relationships and uploads'
      }
    ],
    responses: {
      200: {
        description: `A single ${singular}`,
        content: {
          'application/json': {
            schema: schemaRef
          }
        }
      }
    }
  }

  // Update by ID
  openApiDocument.paths[pathWithId].patch = {
    summary: `Update a ${singular} by ID`,
    operationId: processOperationId(`update-${singular}-by-id`),
    tags: [slug],
    parameters: [{ name: 'id', in: 'path', required: true, schema: { type: 'string' } }],
    requestBody: {
      content: upload
        ? {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary'
                  },
                  _payload: {
                    type: 'string',
                    description: 'JSON-stringified object containing collection fields'
                  }
                }
              }
            }
          }
        : {
            'application/json': {
              schema: schemaRef
            }
          }
    },
    responses: {
      200: {
        description: `Updated ${singular}`,
        content: {
          'application/json': {
            schema: schemaRef
          }
        }
      }
    }
  }

  // Delete by ID
  openApiDocument.paths[pathWithId].delete = {
    summary: `Delete a ${singular} by ID`,
    operationId: processOperationId(`delete-${singular}-by-id`),
    tags: [slug],
    parameters: [{ name: 'id', in: 'path', required: true, schema: { type: 'string' } }],
    responses: {
      204: {
        description: `${singular} deleted`
      }
    }
  }

  // Count path
  const countPath = `${path}/count`
  openApiDocument.paths[countPath] = {}
  openApiDocument.paths[countPath].get = {
    summary: `Count of ${plural}`,
    operationId: processOperationId(`count-${plural}`),
    tags: [slug],
    responses: {
      200: {
        description: `Count of ${plural}`,
        content: {
          'application/json': {
            schema: { type: 'object', properties: { totalDocs: { type: 'integer' } } }
          }
        }
      }
    }
  }

  if (collection.auth) {
    // Auth paths
    // Login path
    openApiDocument.paths[`${path}/login`] = {
      post: {
        summary: 'Login',
        operationId: processOperationId('login'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Login',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Logout path
    openApiDocument.paths[`${path}/logout`] = {
      post: {
        summary: 'Logout',
        operationId: processOperationId('logout'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Logout',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Unlock path
    openApiDocument.paths[`${path}/unlock`] = {
      post: {
        summary: 'Unlock',
        operationId: processOperationId('unlock'),
        tags: ['auth'],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: { type: 'string' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Unlock',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Refresh token path
    openApiDocument.paths[`${path}/refresh-token`] = {
      post: {
        summary: 'Refresh token',
        operationId: processOperationId('refresh-token'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Refresh token',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Current user path
    openApiDocument.paths[`${path}/me`] = {
      get: {
        summary: 'Current user',
        operationId: processOperationId('current-user'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Current user',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Forgot password path
    openApiDocument.paths[`${path}/forgot-password`] = {
      post: {
        summary: 'Forgot password',
        operationId: processOperationId('forgot-password'),
        tags: ['auth'],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: { type: 'string' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Forgot password',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Reset password path
    openApiDocument.paths[`${path}/reset-password`] = {
      post: {
        summary: 'Reset password',
        operationId: processOperationId('reset-password'),
        tags: ['auth'],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  token: { type: 'string' },
                  password: { type: 'string' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Reset password',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Verify token path
    openApiDocument.paths[`${path}/verify/{token}`] = {
      post: {
        summary: 'Verify token',
        operationId: processOperationId('verify-token'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Verify token',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }
  }
}

// Function to generate auth-related paths (no longer used, keeping for reference)
// const generateAuthPath = ({
//   summary,
//   method,
//   requestBody = null
// }: {
//   summary: string
//   method: string
//   requestBody?: { [key: string]: string } | null
// }): OpenAPIPathItem => {
//   const methodKey = method.toLowerCase()
//   const operation: OpenAPIOperation = {
//     summary,
//     operationId: processOperationId(summary),
//     tags: ['auth'],
//     responses: {
//       200: {
//         description: summary,
//         content: {
//           'application/json': {
//             schema: { type: 'object' }
//           }
//         }
//       }
//     }
//   }

//   // If request body is needed, add to the operation
//   if (requestBody) {
//     operation.requestBody = {
//       content: {
//         'application/json': {
//           schema: {
//             type: 'object',
//             properties: requestBody
//           }
//         }
//       }
//     }
//   }

//   // Return the path item with the operation at the specified method
//   return { [methodKey]: operation }
// }

// const generateProperties = (fields) => {
//   const properties = {}

//   fields.forEach((field) => {
//     const fieldType = field.type
//     let openApiType

//     switch (fieldType) {
//       case 'text':
//       case 'title':
//       case 'slug':
//       case 'richText':
//       case 'textarea':
//       case 'code':
//         openApiType = 'string'
//         break

//       case 'boolean': // Checkbox, Radio group, and UI fields are booleans
//       case 'checkbox':
//       case 'radio':
//         openApiType = 'boolean'
//         break

//       case 'number':
//       case 'integer':
//       case 'point':
//         openApiType = 'integer'
//         break

//       case 'email':
//         openApiType = 'string'
//         properties[field.name].format = 'email'
//         break

//       case 'date':
//         openApiType = 'string'
//         properties[field.name].format = 'date'
//         break

//       case 'datetime':
//         openApiType = 'string'
//         properties[field.name].format = 'date-time'
//         break

//       case 'array':
//       case 'select':
//         openApiType = 'array'
//         properties[field.name].items = generateProperties(field.options) // Recursively handle array fields
//         break

//       case 'file':
//       case 'upload':
//         openApiType = 'string'
//         properties[field.name].format = 'uri'
//         break

//       case 'group':
//         openApiType = 'object'
//         properties[field.name].properties = generateProperties(field.fields) // Recursively handle nested groups
//         break

//       case 'relationship':
//         openApiType = 'object'
//         properties[field.name].properties = {
//           // Relationship can link to another collection (so it's an object with the related fields)
//           id: { type: 'string' } // Assuming a related item with a string ID
//         }
//         break

//       case 'join':
//         openApiType = 'array'
//         properties[field.name].items = {
//           type: 'object',
//           properties: {
//             id: { type: 'string' }
//           }
//         }
//         break

//       case 'tabs':
//         openApiType = 'array'
//         properties[field.name].items = { type: 'string' } // Tabs are usually just strings representing tab names
//         break

//       case 'row':
//         openApiType = 'array'
//         properties[field.name].items = { type: 'object' } // Row can be an array of objects (fields inside the row)
//         break

//       case 'collapsible':
//         openApiType = 'object'
//         properties[field.name].properties = generateProperties(field.fields) // Collapsible groups can have nested properties
//         break

//       default:
//         openApiType = 'string' // Default to string if no match
//         break
//     }

//     // Add the field to properties
//     properties[field.name] = {
//       type: openApiType,
//       ...(properties[field.name] || {})
//     }
//   })

//   return properties
// }

// Function to generate OpenAPI properties from fields
// const generateProperties = (fields) => {
//   const properties = {}

//   fields.forEach((field) => {
//     properties[field.name] = {
//       type: field.type === 'text' ? 'string' : field.type
//     }
//   })

//   return properties
// }

export const generateApi = async () => {
  try {
    console.log('🔄 Starting OpenAPI generation...')

    const config = await configPromise
    const collections = config.collections.filter((c) => !ignoreList.has(c.slug))

    console.log(
      `📋 Generating OpenAPI for ${collections.length} collections:`,
      collections.map((c) => c.slug).join(', ')
    )

    // Create TypeScript schema generator
    console.log('🔧 Creating TypeScript schema generator...')
    const generator = createSchemaGenerator()

    if (!generator) {
      console.warn('⚠️  TypeScript schema generation failed, using fallback schemas')
    } else {
      console.log('✅ TypeScript schema generator created successfully')
    }

    // Generate paths for each Payload collection
    console.log('🔄 Processing collections...')
    for (const collection of collections) {
      console.log(`  📝 Processing collection: ${collection.slug}`)
      convertCollectionToOpenApi(collection, generator)
    }

    // Write OpenAPI document to JSON file
    const outputPath = path.resolve(process.cwd(), 'openapi.json')
    fs.writeFileSync(outputPath, JSON.stringify(openApiDocument, null, 2))

    console.log('✅ OpenAPI document generated successfully!')
    console.log(`📄 Output: ${outputPath}`)
    console.log(`📊 Generated ${Object.keys(openApiDocument.paths).length} API paths`)
    console.log(
      `🏗️  Generated ${Object.keys(openApiDocument.components.schemas).length} schemas`
    )
  } catch (error) {
    console.error('❌ Error generating OpenAPI document:', error)
    throw error
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateApi().catch((error) => {
    console.error('❌ Script execution failed:', error)
    process.exit(1)
  })
}
