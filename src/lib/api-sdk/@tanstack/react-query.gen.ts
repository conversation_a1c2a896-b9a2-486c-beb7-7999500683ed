// This file is auto-generated by @hey-api/openapi-ts

import { type Options, deleteUsers, findUsers, updateUsers, createUser, deleteUserById, findUserById, updateUserById, countUsers, login, logout, unlock, refreshToken, currentUser, forgotPassword, resetPassword, verifyToken } from '../sdk.gen';
import { type UseMutationOptions, type DefaultError, queryOptions } from '@tanstack/react-query';
import type { DeleteUsersData, DeleteUsersResponse, FindUsersData, UpdateUsersData, UpdateUsersResponse, CreateUserData, CreateUserResponse, DeleteUserByIdData, DeleteUserByIdResponse, FindUserByIdData, UpdateUserByIdData, UpdateUserByIdResponse, CountUsersData, LoginData, LoginResponse, LogoutData, LogoutResponse, UnlockData, UnlockResponse, RefreshTokenData, RefreshTokenResponse, CurrentUserData, ForgotPasswordData, ForgotPasswordResponse, ResetPasswordData, ResetPasswordResponse, VerifyTokenData, VerifyTokenResponse } from '../types.gen';
import { client as _heyApiClient } from '../client.gen';

/**
 * Delete users
 */
export const deleteUsersMutation = (options?: Partial<Options<DeleteUsersData>>): UseMutationOptions<DeleteUsersResponse, DefaultError, Options<DeleteUsersData>> => {
    const mutationOptions: UseMutationOptions<DeleteUsersResponse, DefaultError, Options<DeleteUsersData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteUsers({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseUrl' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseUrl: (options?.client ?? _heyApiClient).getConfig().baseUrl } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const findUsersQueryKey = (options?: Options<FindUsersData>) => createQueryKey('findUsers', options);

/**
 * List all users
 */
export const findUsersOptions = (options?: Options<FindUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findUsersQueryKey(options)
    });
};

/**
 * Update users
 */
export const updateUsersMutation = (options?: Partial<Options<UpdateUsersData>>): UseMutationOptions<UpdateUsersResponse, DefaultError, Options<UpdateUsersData>> => {
    const mutationOptions: UseMutationOptions<UpdateUsersResponse, DefaultError, Options<UpdateUsersData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUsers({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createUserQueryKey = (options?: Options<CreateUserData>) => createQueryKey('createUser', options);

/**
 * Create a new user
 */
export const createUserOptions = (options?: Options<CreateUserData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createUser({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createUserQueryKey(options)
    });
};

/**
 * Create a new user
 */
export const createUserMutation = (options?: Partial<Options<CreateUserData>>): UseMutationOptions<CreateUserResponse, DefaultError, Options<CreateUserData>> => {
    const mutationOptions: UseMutationOptions<CreateUserResponse, DefaultError, Options<CreateUserData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createUser({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a user by ID
 */
export const deleteUserByIdMutation = (options?: Partial<Options<DeleteUserByIdData>>): UseMutationOptions<DeleteUserByIdResponse, DefaultError, Options<DeleteUserByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteUserByIdResponse, DefaultError, Options<DeleteUserByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteUserById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findUserByIdQueryKey = (options: Options<FindUserByIdData>) => createQueryKey('findUserById', options);

/**
 * Retrieve a user by ID
 */
export const findUserByIdOptions = (options: Options<FindUserByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findUserById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findUserByIdQueryKey(options)
    });
};

/**
 * Update a user by ID
 */
export const updateUserByIdMutation = (options?: Partial<Options<UpdateUserByIdData>>): UseMutationOptions<UpdateUserByIdResponse, DefaultError, Options<UpdateUserByIdData>> => {
    const mutationOptions: UseMutationOptions<UpdateUserByIdResponse, DefaultError, Options<UpdateUserByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUserById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const countUsersQueryKey = (options?: Options<CountUsersData>) => createQueryKey('countUsers', options);

/**
 * Count of users
 */
export const countUsersOptions = (options?: Options<CountUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await countUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: countUsersQueryKey(options)
    });
};

export const loginQueryKey = (options?: Options<LoginData>) => createQueryKey('login', options);

/**
 * Login
 */
export const loginOptions = (options?: Options<LoginData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await login({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginQueryKey(options)
    });
};

/**
 * Login
 */
export const loginMutation = (options?: Partial<Options<LoginData>>): UseMutationOptions<LoginResponse, DefaultError, Options<LoginData>> => {
    const mutationOptions: UseMutationOptions<LoginResponse, DefaultError, Options<LoginData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await login({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const logoutQueryKey = (options?: Options<LogoutData>) => createQueryKey('logout', options);

/**
 * Logout
 */
export const logoutOptions = (options?: Options<LogoutData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await logout({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: logoutQueryKey(options)
    });
};

/**
 * Logout
 */
export const logoutMutation = (options?: Partial<Options<LogoutData>>): UseMutationOptions<LogoutResponse, DefaultError, Options<LogoutData>> => {
    const mutationOptions: UseMutationOptions<LogoutResponse, DefaultError, Options<LogoutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await logout({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const unlockQueryKey = (options?: Options<UnlockData>) => createQueryKey('unlock', options);

/**
 * Unlock
 */
export const unlockOptions = (options?: Options<UnlockData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await unlock({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: unlockQueryKey(options)
    });
};

/**
 * Unlock
 */
export const unlockMutation = (options?: Partial<Options<UnlockData>>): UseMutationOptions<UnlockResponse, DefaultError, Options<UnlockData>> => {
    const mutationOptions: UseMutationOptions<UnlockResponse, DefaultError, Options<UnlockData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await unlock({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const refreshTokenQueryKey = (options?: Options<RefreshTokenData>) => createQueryKey('refreshToken', options);

/**
 * Refresh token
 */
export const refreshTokenOptions = (options?: Options<RefreshTokenData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await refreshToken({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: refreshTokenQueryKey(options)
    });
};

/**
 * Refresh token
 */
export const refreshTokenMutation = (options?: Partial<Options<RefreshTokenData>>): UseMutationOptions<RefreshTokenResponse, DefaultError, Options<RefreshTokenData>> => {
    const mutationOptions: UseMutationOptions<RefreshTokenResponse, DefaultError, Options<RefreshTokenData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await refreshToken({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const currentUserQueryKey = (options?: Options<CurrentUserData>) => createQueryKey('currentUser', options);

/**
 * Current user
 */
export const currentUserOptions = (options?: Options<CurrentUserData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await currentUser({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: currentUserQueryKey(options)
    });
};

export const forgotPasswordQueryKey = (options?: Options<ForgotPasswordData>) => createQueryKey('forgotPassword', options);

/**
 * Forgot password
 */
export const forgotPasswordOptions = (options?: Options<ForgotPasswordData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await forgotPassword({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: forgotPasswordQueryKey(options)
    });
};

/**
 * Forgot password
 */
export const forgotPasswordMutation = (options?: Partial<Options<ForgotPasswordData>>): UseMutationOptions<ForgotPasswordResponse, DefaultError, Options<ForgotPasswordData>> => {
    const mutationOptions: UseMutationOptions<ForgotPasswordResponse, DefaultError, Options<ForgotPasswordData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await forgotPassword({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const resetPasswordQueryKey = (options?: Options<ResetPasswordData>) => createQueryKey('resetPassword', options);

/**
 * Reset password
 */
export const resetPasswordOptions = (options?: Options<ResetPasswordData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await resetPassword({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: resetPasswordQueryKey(options)
    });
};

/**
 * Reset password
 */
export const resetPasswordMutation = (options?: Partial<Options<ResetPasswordData>>): UseMutationOptions<ResetPasswordResponse, DefaultError, Options<ResetPasswordData>> => {
    const mutationOptions: UseMutationOptions<ResetPasswordResponse, DefaultError, Options<ResetPasswordData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await resetPassword({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const verifyTokenQueryKey = (options?: Options<VerifyTokenData>) => createQueryKey('verifyToken', options);

/**
 * Verify token
 */
export const verifyTokenOptions = (options?: Options<VerifyTokenData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await verifyToken({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: verifyTokenQueryKey(options)
    });
};

/**
 * Verify token
 */
export const verifyTokenMutation = (options?: Partial<Options<VerifyTokenData>>): UseMutationOptions<VerifyTokenResponse, DefaultError, Options<VerifyTokenData>> => {
    const mutationOptions: UseMutationOptions<VerifyTokenResponse, DefaultError, Options<VerifyTokenData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await verifyToken({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};