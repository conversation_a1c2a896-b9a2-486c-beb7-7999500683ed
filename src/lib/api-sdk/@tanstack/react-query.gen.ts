// This file is auto-generated by @hey-api/openapi-ts

import { type Options, deletePages, findPages, updatePages, createPage, deletePageById, findPageById, updatePageById, countPages, deletePosts, findPosts, updatePosts, createPost, deletePostById, findPostById, updatePostById, countPosts, deleteMedia, findMedia, updateMedia, createMedia, deleteMediaById, findMediaById, updateMediaById, countMedia, deleteCategories, findCategories, updateCategories, createCategory, deleteCategoryById, findCategoryById, updateCategoryById, countCategories, deleteUsers, findUsers, updateUsers, createUser, deleteUserById, findUserById, updateUserById, countUsers, login, logout, unlock, refreshToken, currentUser, forgotPassword, resetPassword, verifyToken } from '../sdk.gen';
import { type UseMutationOptions, type DefaultError, queryOptions, infiniteQueryOptions, type InfiniteData } from '@tanstack/react-query';
import type { DeletePagesData, DeletePagesResponse, FindPagesData, FindPagesResponse, UpdatePagesData, UpdatePagesResponse, CreatePageData, CreatePageResponse, DeletePageByIdData, DeletePageByIdResponse, FindPageByIdData, UpdatePageByIdData, UpdatePageByIdResponse, CountPagesData, DeletePostsData, DeletePostsResponse, FindPostsData, FindPostsResponse, UpdatePostsData, UpdatePostsResponse, CreatePostData, CreatePostResponse, DeletePostByIdData, DeletePostByIdResponse, FindPostByIdData, UpdatePostByIdData, UpdatePostByIdResponse, CountPostsData, DeleteMediaData, DeleteMediaResponse, FindMediaData, FindMediaResponse, UpdateMediaData, UpdateMediaResponse, CreateMediaData, CreateMediaResponse, DeleteMediaByIdData, DeleteMediaByIdResponse, FindMediaByIdData, UpdateMediaByIdData, UpdateMediaByIdResponse, CountMediaData, DeleteCategoriesData, DeleteCategoriesResponse, FindCategoriesData, FindCategoriesResponse, UpdateCategoriesData, UpdateCategoriesResponse, CreateCategoryData, CreateCategoryResponse, DeleteCategoryByIdData, DeleteCategoryByIdResponse, FindCategoryByIdData, UpdateCategoryByIdData, UpdateCategoryByIdResponse, CountCategoriesData, DeleteUsersData, DeleteUsersResponse, FindUsersData, FindUsersResponse, UpdateUsersData, UpdateUsersResponse, CreateUserData, CreateUserResponse, DeleteUserByIdData, DeleteUserByIdResponse, FindUserByIdData, UpdateUserByIdData, UpdateUserByIdResponse, CountUsersData, LoginData, LoginResponse, LogoutData, LogoutResponse, UnlockData, UnlockResponse, RefreshTokenData, RefreshTokenResponse, CurrentUserData, ForgotPasswordData, ForgotPasswordResponse, ResetPasswordData, ResetPasswordResponse, VerifyTokenData, VerifyTokenResponse } from '../types.gen';
import { client as _heyApiClient } from '../client.gen';

/**
 * Delete pages
 */
export const deletePagesMutation = (options?: Partial<Options<DeletePagesData>>): UseMutationOptions<DeletePagesResponse, DefaultError, Options<DeletePagesData>> => {
    const mutationOptions: UseMutationOptions<DeletePagesResponse, DefaultError, Options<DeletePagesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deletePages({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseUrl' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseUrl: (options?.client ?? _heyApiClient).getConfig().baseUrl } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const findPagesQueryKey = (options?: Options<FindPagesData>) => createQueryKey('findPages', options);

/**
 * List all pages
 */
export const findPagesOptions = (options?: Options<FindPagesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findPages({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findPagesQueryKey(options)
    });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>>(queryKey: QueryKey<Options>, page: K) => {
    const params = queryKey[0];
    if (page.body) {
        params.body = {
            ...queryKey[0].body as any,
            ...page.body as any
        };
    }
    if (page.headers) {
        params.headers = {
            ...queryKey[0].headers,
            ...page.headers
        };
    }
    if (page.path) {
        params.path = {
            ...queryKey[0].path as any,
            ...page.path as any
        };
    }
    if (page.query) {
        params.query = {
            ...queryKey[0].query as any,
            ...page.query as any
        };
    }
    return params as unknown as typeof page;
};

export const findPagesInfiniteQueryKey = (options?: Options<FindPagesData>): QueryKey<Options<FindPagesData>> => createQueryKey('findPages', options, true);

/**
 * List all pages
 */
export const findPagesInfiniteOptions = (options?: Options<FindPagesData>) => {
    return infiniteQueryOptions<FindPagesResponse, DefaultError, InfiniteData<FindPagesResponse>, QueryKey<Options<FindPagesData>>, number | Pick<QueryKey<Options<FindPagesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<FindPagesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await findPages({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findPagesInfiniteQueryKey(options)
    });
};

/**
 * Update pages
 */
export const updatePagesMutation = (options?: Partial<Options<UpdatePagesData>>): UseMutationOptions<UpdatePagesResponse, DefaultError, Options<UpdatePagesData>> => {
    const mutationOptions: UseMutationOptions<UpdatePagesResponse, DefaultError, Options<UpdatePagesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updatePages({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createPageQueryKey = (options?: Options<CreatePageData>) => createQueryKey('createPage', options);

/**
 * Create a new page
 */
export const createPageOptions = (options?: Options<CreatePageData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createPage({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createPageQueryKey(options)
    });
};

/**
 * Create a new page
 */
export const createPageMutation = (options?: Partial<Options<CreatePageData>>): UseMutationOptions<CreatePageResponse, DefaultError, Options<CreatePageData>> => {
    const mutationOptions: UseMutationOptions<CreatePageResponse, DefaultError, Options<CreatePageData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createPage({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a page by ID
 */
export const deletePageByIdMutation = (options?: Partial<Options<DeletePageByIdData>>): UseMutationOptions<DeletePageByIdResponse, DefaultError, Options<DeletePageByIdData>> => {
    const mutationOptions: UseMutationOptions<DeletePageByIdResponse, DefaultError, Options<DeletePageByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deletePageById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findPageByIdQueryKey = (options: Options<FindPageByIdData>) => createQueryKey('findPageById', options);

/**
 * Retrieve a page by ID
 */
export const findPageByIdOptions = (options: Options<FindPageByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findPageById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findPageByIdQueryKey(options)
    });
};

/**
 * Update a page by ID
 */
export const updatePageByIdMutation = (options?: Partial<Options<UpdatePageByIdData>>): UseMutationOptions<UpdatePageByIdResponse, DefaultError, Options<UpdatePageByIdData>> => {
    const mutationOptions: UseMutationOptions<UpdatePageByIdResponse, DefaultError, Options<UpdatePageByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updatePageById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const countPagesQueryKey = (options?: Options<CountPagesData>) => createQueryKey('countPages', options);

/**
 * Count of pages
 */
export const countPagesOptions = (options?: Options<CountPagesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await countPages({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: countPagesQueryKey(options)
    });
};

/**
 * Delete posts
 */
export const deletePostsMutation = (options?: Partial<Options<DeletePostsData>>): UseMutationOptions<DeletePostsResponse, DefaultError, Options<DeletePostsData>> => {
    const mutationOptions: UseMutationOptions<DeletePostsResponse, DefaultError, Options<DeletePostsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deletePosts({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findPostsQueryKey = (options?: Options<FindPostsData>) => createQueryKey('findPosts', options);

/**
 * List all posts
 */
export const findPostsOptions = (options?: Options<FindPostsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findPosts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findPostsQueryKey(options)
    });
};

export const findPostsInfiniteQueryKey = (options?: Options<FindPostsData>): QueryKey<Options<FindPostsData>> => createQueryKey('findPosts', options, true);

/**
 * List all posts
 */
export const findPostsInfiniteOptions = (options?: Options<FindPostsData>) => {
    return infiniteQueryOptions<FindPostsResponse, DefaultError, InfiniteData<FindPostsResponse>, QueryKey<Options<FindPostsData>>, number | Pick<QueryKey<Options<FindPostsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<FindPostsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await findPosts({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findPostsInfiniteQueryKey(options)
    });
};

/**
 * Update posts
 */
export const updatePostsMutation = (options?: Partial<Options<UpdatePostsData>>): UseMutationOptions<UpdatePostsResponse, DefaultError, Options<UpdatePostsData>> => {
    const mutationOptions: UseMutationOptions<UpdatePostsResponse, DefaultError, Options<UpdatePostsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updatePosts({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createPostQueryKey = (options?: Options<CreatePostData>) => createQueryKey('createPost', options);

/**
 * Create a new post
 */
export const createPostOptions = (options?: Options<CreatePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createPostQueryKey(options)
    });
};

/**
 * Create a new post
 */
export const createPostMutation = (options?: Partial<Options<CreatePostData>>): UseMutationOptions<CreatePostResponse, DefaultError, Options<CreatePostData>> => {
    const mutationOptions: UseMutationOptions<CreatePostResponse, DefaultError, Options<CreatePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a post by ID
 */
export const deletePostByIdMutation = (options?: Partial<Options<DeletePostByIdData>>): UseMutationOptions<DeletePostByIdResponse, DefaultError, Options<DeletePostByIdData>> => {
    const mutationOptions: UseMutationOptions<DeletePostByIdResponse, DefaultError, Options<DeletePostByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deletePostById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findPostByIdQueryKey = (options: Options<FindPostByIdData>) => createQueryKey('findPostById', options);

/**
 * Retrieve a post by ID
 */
export const findPostByIdOptions = (options: Options<FindPostByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findPostById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findPostByIdQueryKey(options)
    });
};

/**
 * Update a post by ID
 */
export const updatePostByIdMutation = (options?: Partial<Options<UpdatePostByIdData>>): UseMutationOptions<UpdatePostByIdResponse, DefaultError, Options<UpdatePostByIdData>> => {
    const mutationOptions: UseMutationOptions<UpdatePostByIdResponse, DefaultError, Options<UpdatePostByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updatePostById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const countPostsQueryKey = (options?: Options<CountPostsData>) => createQueryKey('countPosts', options);

/**
 * Count of posts
 */
export const countPostsOptions = (options?: Options<CountPostsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await countPosts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: countPostsQueryKey(options)
    });
};

/**
 * Delete media
 */
export const deleteMediaMutation = (options?: Partial<Options<DeleteMediaData>>): UseMutationOptions<DeleteMediaResponse, DefaultError, Options<DeleteMediaData>> => {
    const mutationOptions: UseMutationOptions<DeleteMediaResponse, DefaultError, Options<DeleteMediaData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteMedia({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findMediaQueryKey = (options?: Options<FindMediaData>) => createQueryKey('findMedia', options);

/**
 * List all media
 */
export const findMediaOptions = (options?: Options<FindMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findMediaQueryKey(options)
    });
};

export const findMediaInfiniteQueryKey = (options?: Options<FindMediaData>): QueryKey<Options<FindMediaData>> => createQueryKey('findMedia', options, true);

/**
 * List all media
 */
export const findMediaInfiniteOptions = (options?: Options<FindMediaData>) => {
    return infiniteQueryOptions<FindMediaResponse, DefaultError, InfiniteData<FindMediaResponse>, QueryKey<Options<FindMediaData>>, number | Pick<QueryKey<Options<FindMediaData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<FindMediaData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await findMedia({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findMediaInfiniteQueryKey(options)
    });
};

/**
 * Update media
 */
export const updateMediaMutation = (options?: Partial<Options<UpdateMediaData>>): UseMutationOptions<UpdateMediaResponse, DefaultError, Options<UpdateMediaData>> => {
    const mutationOptions: UseMutationOptions<UpdateMediaResponse, DefaultError, Options<UpdateMediaData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateMedia({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createMediaQueryKey = (options?: Options<CreateMediaData>) => createQueryKey('createMedia', options);

/**
 * Create a new media
 */
export const createMediaOptions = (options?: Options<CreateMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createMediaQueryKey(options)
    });
};

/**
 * Create a new media
 */
export const createMediaMutation = (options?: Partial<Options<CreateMediaData>>): UseMutationOptions<CreateMediaResponse, DefaultError, Options<CreateMediaData>> => {
    const mutationOptions: UseMutationOptions<CreateMediaResponse, DefaultError, Options<CreateMediaData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createMedia({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a media by ID
 */
export const deleteMediaByIdMutation = (options?: Partial<Options<DeleteMediaByIdData>>): UseMutationOptions<DeleteMediaByIdResponse, DefaultError, Options<DeleteMediaByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteMediaByIdResponse, DefaultError, Options<DeleteMediaByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteMediaById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findMediaByIdQueryKey = (options: Options<FindMediaByIdData>) => createQueryKey('findMediaById', options);

/**
 * Retrieve a media by ID
 */
export const findMediaByIdOptions = (options: Options<FindMediaByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findMediaById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findMediaByIdQueryKey(options)
    });
};

/**
 * Update a media by ID
 */
export const updateMediaByIdMutation = (options?: Partial<Options<UpdateMediaByIdData>>): UseMutationOptions<UpdateMediaByIdResponse, DefaultError, Options<UpdateMediaByIdData>> => {
    const mutationOptions: UseMutationOptions<UpdateMediaByIdResponse, DefaultError, Options<UpdateMediaByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateMediaById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const countMediaQueryKey = (options?: Options<CountMediaData>) => createQueryKey('countMedia', options);

/**
 * Count of media
 */
export const countMediaOptions = (options?: Options<CountMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await countMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: countMediaQueryKey(options)
    });
};

/**
 * Delete categories
 */
export const deleteCategoriesMutation = (options?: Partial<Options<DeleteCategoriesData>>): UseMutationOptions<DeleteCategoriesResponse, DefaultError, Options<DeleteCategoriesData>> => {
    const mutationOptions: UseMutationOptions<DeleteCategoriesResponse, DefaultError, Options<DeleteCategoriesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteCategories({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findCategoriesQueryKey = (options?: Options<FindCategoriesData>) => createQueryKey('findCategories', options);

/**
 * List all categories
 */
export const findCategoriesOptions = (options?: Options<FindCategoriesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findCategories({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findCategoriesQueryKey(options)
    });
};

export const findCategoriesInfiniteQueryKey = (options?: Options<FindCategoriesData>): QueryKey<Options<FindCategoriesData>> => createQueryKey('findCategories', options, true);

/**
 * List all categories
 */
export const findCategoriesInfiniteOptions = (options?: Options<FindCategoriesData>) => {
    return infiniteQueryOptions<FindCategoriesResponse, DefaultError, InfiniteData<FindCategoriesResponse>, QueryKey<Options<FindCategoriesData>>, number | Pick<QueryKey<Options<FindCategoriesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<FindCategoriesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await findCategories({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findCategoriesInfiniteQueryKey(options)
    });
};

/**
 * Update categories
 */
export const updateCategoriesMutation = (options?: Partial<Options<UpdateCategoriesData>>): UseMutationOptions<UpdateCategoriesResponse, DefaultError, Options<UpdateCategoriesData>> => {
    const mutationOptions: UseMutationOptions<UpdateCategoriesResponse, DefaultError, Options<UpdateCategoriesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCategories({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createCategoryQueryKey = (options?: Options<CreateCategoryData>) => createQueryKey('createCategory', options);

/**
 * Create a new category
 */
export const createCategoryOptions = (options?: Options<CreateCategoryData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createCategory({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createCategoryQueryKey(options)
    });
};

/**
 * Create a new category
 */
export const createCategoryMutation = (options?: Partial<Options<CreateCategoryData>>): UseMutationOptions<CreateCategoryResponse, DefaultError, Options<CreateCategoryData>> => {
    const mutationOptions: UseMutationOptions<CreateCategoryResponse, DefaultError, Options<CreateCategoryData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createCategory({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a category by ID
 */
export const deleteCategoryByIdMutation = (options?: Partial<Options<DeleteCategoryByIdData>>): UseMutationOptions<DeleteCategoryByIdResponse, DefaultError, Options<DeleteCategoryByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteCategoryByIdResponse, DefaultError, Options<DeleteCategoryByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteCategoryById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findCategoryByIdQueryKey = (options: Options<FindCategoryByIdData>) => createQueryKey('findCategoryById', options);

/**
 * Retrieve a category by ID
 */
export const findCategoryByIdOptions = (options: Options<FindCategoryByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findCategoryById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findCategoryByIdQueryKey(options)
    });
};

/**
 * Update a category by ID
 */
export const updateCategoryByIdMutation = (options?: Partial<Options<UpdateCategoryByIdData>>): UseMutationOptions<UpdateCategoryByIdResponse, DefaultError, Options<UpdateCategoryByIdData>> => {
    const mutationOptions: UseMutationOptions<UpdateCategoryByIdResponse, DefaultError, Options<UpdateCategoryByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCategoryById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const countCategoriesQueryKey = (options?: Options<CountCategoriesData>) => createQueryKey('countCategories', options);

/**
 * Count of categories
 */
export const countCategoriesOptions = (options?: Options<CountCategoriesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await countCategories({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: countCategoriesQueryKey(options)
    });
};

/**
 * Delete users
 */
export const deleteUsersMutation = (options?: Partial<Options<DeleteUsersData>>): UseMutationOptions<DeleteUsersResponse, DefaultError, Options<DeleteUsersData>> => {
    const mutationOptions: UseMutationOptions<DeleteUsersResponse, DefaultError, Options<DeleteUsersData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteUsers({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findUsersQueryKey = (options?: Options<FindUsersData>) => createQueryKey('findUsers', options);

/**
 * List all users
 */
export const findUsersOptions = (options?: Options<FindUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findUsersQueryKey(options)
    });
};

export const findUsersInfiniteQueryKey = (options?: Options<FindUsersData>): QueryKey<Options<FindUsersData>> => createQueryKey('findUsers', options, true);

/**
 * List all users
 */
export const findUsersInfiniteOptions = (options?: Options<FindUsersData>) => {
    return infiniteQueryOptions<FindUsersResponse, DefaultError, InfiniteData<FindUsersResponse>, QueryKey<Options<FindUsersData>>, number | Pick<QueryKey<Options<FindUsersData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<FindUsersData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await findUsers({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findUsersInfiniteQueryKey(options)
    });
};

/**
 * Update users
 */
export const updateUsersMutation = (options?: Partial<Options<UpdateUsersData>>): UseMutationOptions<UpdateUsersResponse, DefaultError, Options<UpdateUsersData>> => {
    const mutationOptions: UseMutationOptions<UpdateUsersResponse, DefaultError, Options<UpdateUsersData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUsers({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createUserQueryKey = (options?: Options<CreateUserData>) => createQueryKey('createUser', options);

/**
 * Create a new user
 */
export const createUserOptions = (options?: Options<CreateUserData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createUser({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createUserQueryKey(options)
    });
};

/**
 * Create a new user
 */
export const createUserMutation = (options?: Partial<Options<CreateUserData>>): UseMutationOptions<CreateUserResponse, DefaultError, Options<CreateUserData>> => {
    const mutationOptions: UseMutationOptions<CreateUserResponse, DefaultError, Options<CreateUserData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createUser({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a user by ID
 */
export const deleteUserByIdMutation = (options?: Partial<Options<DeleteUserByIdData>>): UseMutationOptions<DeleteUserByIdResponse, DefaultError, Options<DeleteUserByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteUserByIdResponse, DefaultError, Options<DeleteUserByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteUserById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const findUserByIdQueryKey = (options: Options<FindUserByIdData>) => createQueryKey('findUserById', options);

/**
 * Retrieve a user by ID
 */
export const findUserByIdOptions = (options: Options<FindUserByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findUserById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findUserByIdQueryKey(options)
    });
};

/**
 * Update a user by ID
 */
export const updateUserByIdMutation = (options?: Partial<Options<UpdateUserByIdData>>): UseMutationOptions<UpdateUserByIdResponse, DefaultError, Options<UpdateUserByIdData>> => {
    const mutationOptions: UseMutationOptions<UpdateUserByIdResponse, DefaultError, Options<UpdateUserByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUserById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const countUsersQueryKey = (options?: Options<CountUsersData>) => createQueryKey('countUsers', options);

/**
 * Count of users
 */
export const countUsersOptions = (options?: Options<CountUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await countUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: countUsersQueryKey(options)
    });
};

export const loginQueryKey = (options?: Options<LoginData>) => createQueryKey('login', options);

/**
 * Login
 */
export const loginOptions = (options?: Options<LoginData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await login({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginQueryKey(options)
    });
};

/**
 * Login
 */
export const loginMutation = (options?: Partial<Options<LoginData>>): UseMutationOptions<LoginResponse, DefaultError, Options<LoginData>> => {
    const mutationOptions: UseMutationOptions<LoginResponse, DefaultError, Options<LoginData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await login({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const logoutQueryKey = (options?: Options<LogoutData>) => createQueryKey('logout', options);

/**
 * Logout
 */
export const logoutOptions = (options?: Options<LogoutData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await logout({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: logoutQueryKey(options)
    });
};

/**
 * Logout
 */
export const logoutMutation = (options?: Partial<Options<LogoutData>>): UseMutationOptions<LogoutResponse, DefaultError, Options<LogoutData>> => {
    const mutationOptions: UseMutationOptions<LogoutResponse, DefaultError, Options<LogoutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await logout({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const unlockQueryKey = (options?: Options<UnlockData>) => createQueryKey('unlock', options);

/**
 * Unlock
 */
export const unlockOptions = (options?: Options<UnlockData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await unlock({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: unlockQueryKey(options)
    });
};

/**
 * Unlock
 */
export const unlockMutation = (options?: Partial<Options<UnlockData>>): UseMutationOptions<UnlockResponse, DefaultError, Options<UnlockData>> => {
    const mutationOptions: UseMutationOptions<UnlockResponse, DefaultError, Options<UnlockData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await unlock({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const refreshTokenQueryKey = (options?: Options<RefreshTokenData>) => createQueryKey('refreshToken', options);

/**
 * Refresh token
 */
export const refreshTokenOptions = (options?: Options<RefreshTokenData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await refreshToken({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: refreshTokenQueryKey(options)
    });
};

/**
 * Refresh token
 */
export const refreshTokenMutation = (options?: Partial<Options<RefreshTokenData>>): UseMutationOptions<RefreshTokenResponse, DefaultError, Options<RefreshTokenData>> => {
    const mutationOptions: UseMutationOptions<RefreshTokenResponse, DefaultError, Options<RefreshTokenData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await refreshToken({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const currentUserQueryKey = (options?: Options<CurrentUserData>) => createQueryKey('currentUser', options);

/**
 * Current user
 */
export const currentUserOptions = (options?: Options<CurrentUserData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await currentUser({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: currentUserQueryKey(options)
    });
};

export const forgotPasswordQueryKey = (options?: Options<ForgotPasswordData>) => createQueryKey('forgotPassword', options);

/**
 * Forgot password
 */
export const forgotPasswordOptions = (options?: Options<ForgotPasswordData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await forgotPassword({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: forgotPasswordQueryKey(options)
    });
};

/**
 * Forgot password
 */
export const forgotPasswordMutation = (options?: Partial<Options<ForgotPasswordData>>): UseMutationOptions<ForgotPasswordResponse, DefaultError, Options<ForgotPasswordData>> => {
    const mutationOptions: UseMutationOptions<ForgotPasswordResponse, DefaultError, Options<ForgotPasswordData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await forgotPassword({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const resetPasswordQueryKey = (options?: Options<ResetPasswordData>) => createQueryKey('resetPassword', options);

/**
 * Reset password
 */
export const resetPasswordOptions = (options?: Options<ResetPasswordData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await resetPassword({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: resetPasswordQueryKey(options)
    });
};

/**
 * Reset password
 */
export const resetPasswordMutation = (options?: Partial<Options<ResetPasswordData>>): UseMutationOptions<ResetPasswordResponse, DefaultError, Options<ResetPasswordData>> => {
    const mutationOptions: UseMutationOptions<ResetPasswordResponse, DefaultError, Options<ResetPasswordData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await resetPassword({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const verifyTokenQueryKey = (options?: Options<VerifyTokenData>) => createQueryKey('verifyToken', options);

/**
 * Verify token
 */
export const verifyTokenOptions = (options?: Options<VerifyTokenData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await verifyToken({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: verifyTokenQueryKey(options)
    });
};

/**
 * Verify token
 */
export const verifyTokenMutation = (options?: Partial<Options<VerifyTokenData>>): UseMutationOptions<VerifyTokenResponse, DefaultError, Options<VerifyTokenData>> => {
    const mutationOptions: UseMutationOptions<VerifyTokenResponse, DefaultError, Options<VerifyTokenData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await verifyToken({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};