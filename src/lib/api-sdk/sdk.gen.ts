// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-next';
import type { DeleteUsersData, DeleteUsersResponse, FindUsersData, FindUsersResponse, UpdateUsersData, UpdateUsersResponse, CreateUserData, CreateUserResponse, DeleteUserByIdData, DeleteUserByIdResponse, FindUserByIdData, FindUserByIdResponse, UpdateUserByIdData, UpdateUserByIdResponse, CountUsersData, CountUsersResponse, LoginData, LoginResponse, LogoutData, LogoutResponse, UnlockData, UnlockResponse, RefreshTokenData, RefreshTokenResponse, CurrentUserData, CurrentUserResponse, ForgotPasswordData, ForgotPasswordResponse, ResetPasswordData, ResetPasswordResponse, VerifyTokenData, VerifyTokenResponse } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Delete users
 */
export const deleteUsers = <ThrowOnError extends boolean = false>(options?: Options<DeleteUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteUsersResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options
    });
};

/**
 * List all users
 */
export const findUsers = <ThrowOnError extends boolean = false>(options?: Options<FindUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<FindUsersResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options
    });
};

/**
 * Update users
 */
export const updateUsers = <ThrowOnError extends boolean = false>(options?: Options<UpdateUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<UpdateUsersResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Create a new user
 */
export const createUser = <ThrowOnError extends boolean = false>(options?: Options<CreateUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CreateUserResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a user by ID
 */
export const deleteUserById = <ThrowOnError extends boolean = false>(options: Options<DeleteUserByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteUserByIdResponse, unknown, ThrowOnError>({
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Retrieve a user by ID
 */
export const findUserById = <ThrowOnError extends boolean = false>(options: Options<FindUserByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FindUserByIdResponse, unknown, ThrowOnError>({
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Update a user by ID
 */
export const updateUserById = <ThrowOnError extends boolean = false>(options: Options<UpdateUserByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateUserByIdResponse, unknown, ThrowOnError>({
        url: '/api/users/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Count of users
 */
export const countUsers = <ThrowOnError extends boolean = false>(options?: Options<CountUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CountUsersResponse, unknown, ThrowOnError>({
        url: '/api/users/count',
        ...options
    });
};

/**
 * Login
 */
export const login = <ThrowOnError extends boolean = false>(options?: Options<LoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<LoginResponse, unknown, ThrowOnError>({
        url: '/api/users/login',
        ...options
    });
};

/**
 * Logout
 */
export const logout = <ThrowOnError extends boolean = false>(options?: Options<LogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<LogoutResponse, unknown, ThrowOnError>({
        url: '/api/users/logout',
        ...options
    });
};

/**
 * Unlock
 */
export const unlock = <ThrowOnError extends boolean = false>(options?: Options<UnlockData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<UnlockResponse, unknown, ThrowOnError>({
        url: '/api/users/unlock',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Refresh token
 */
export const refreshToken = <ThrowOnError extends boolean = false>(options?: Options<RefreshTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<RefreshTokenResponse, unknown, ThrowOnError>({
        url: '/api/users/refresh-token',
        ...options
    });
};

/**
 * Current user
 */
export const currentUser = <ThrowOnError extends boolean = false>(options?: Options<CurrentUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CurrentUserResponse, unknown, ThrowOnError>({
        url: '/api/users/me',
        ...options
    });
};

/**
 * Forgot password
 */
export const forgotPassword = <ThrowOnError extends boolean = false>(options?: Options<ForgotPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<ForgotPasswordResponse, unknown, ThrowOnError>({
        url: '/api/users/forgot-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Reset password
 */
export const resetPassword = <ThrowOnError extends boolean = false>(options?: Options<ResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<ResetPasswordResponse, unknown, ThrowOnError>({
        url: '/api/users/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Verify token
 */
export const verifyToken = <ThrowOnError extends boolean = false>(options?: Options<VerifyTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<VerifyTokenResponse, unknown, ThrowOnError>({
        url: '/api/users/verify/{token}',
        ...options
    });
};