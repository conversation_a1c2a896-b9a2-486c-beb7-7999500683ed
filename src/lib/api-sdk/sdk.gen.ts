// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-fetch';
import type { DeletePagesData, DeletePagesResponse, FindPagesData, FindPagesResponse, UpdatePagesData, UpdatePagesResponse, CreatePageData, CreatePageResponse, DeletePageByIdData, DeletePageByIdResponse, FindPageByIdData, FindPageByIdResponse, UpdatePageByIdData, UpdatePageByIdResponse, CountPagesData, CountPagesResponse, DeletePostsData, DeletePostsResponse, FindPostsData, FindPostsResponse, UpdatePostsData, UpdatePostsResponse, CreatePostData, CreatePostResponse, DeletePostByIdData, DeletePostByIdResponse, FindPostByIdData, FindPostByIdResponse, UpdatePostByIdData, UpdatePostByIdResponse, CountPostsData, CountPostsResponse, DeleteMediaData, DeleteMediaResponse, FindMediaData, FindMediaResponse, UpdateMediaData, UpdateMediaResponse, CreateMediaData, CreateMediaResponse, DeleteMediaByIdData, DeleteMediaByIdResponse, FindMediaByIdData, FindMediaByIdResponse, UpdateMediaByIdData, UpdateMediaByIdResponse, CountMediaData, CountMediaResponse, DeleteCategoriesData, DeleteCategoriesResponse, FindCategoriesData, FindCategoriesResponse, UpdateCategoriesData, UpdateCategoriesResponse, CreateCategoryData, CreateCategoryResponse, DeleteCategoryByIdData, DeleteCategoryByIdResponse, FindCategoryByIdData, FindCategoryByIdResponse, UpdateCategoryByIdData, UpdateCategoryByIdResponse, CountCategoriesData, CountCategoriesResponse, DeleteUsersData, DeleteUsersResponse, FindUsersData, FindUsersResponse, UpdateUsersData, UpdateUsersResponse, CreateUserData, CreateUserResponse, DeleteUserByIdData, DeleteUserByIdResponse, FindUserByIdData, FindUserByIdResponse, UpdateUserByIdData, UpdateUserByIdResponse, CountUsersData, CountUsersResponse, LoginData, LoginResponse, LogoutData, LogoutResponse, UnlockData, UnlockResponse, RefreshTokenData, RefreshTokenResponse, CurrentUserData, CurrentUserResponse, ForgotPasswordData, ForgotPasswordResponse, ResetPasswordData, ResetPasswordResponse, VerifyTokenData, VerifyTokenResponse } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Delete pages
 */
export const deletePages = <ThrowOnError extends boolean = false>(options?: Options<DeletePagesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeletePagesResponse, unknown, ThrowOnError>({
        url: '/api/pages',
        ...options
    });
};

/**
 * List all pages
 */
export const findPages = <ThrowOnError extends boolean = false>(options?: Options<FindPagesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<FindPagesResponse, unknown, ThrowOnError>({
        url: '/api/pages',
        ...options
    });
};

/**
 * Update pages
 */
export const updatePages = <ThrowOnError extends boolean = false>(options?: Options<UpdatePagesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<UpdatePagesResponse, unknown, ThrowOnError>({
        url: '/api/pages',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Create a new page
 */
export const createPage = <ThrowOnError extends boolean = false>(options?: Options<CreatePageData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CreatePageResponse, unknown, ThrowOnError>({
        url: '/api/pages',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a page by ID
 */
export const deletePageById = <ThrowOnError extends boolean = false>(options: Options<DeletePageByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeletePageByIdResponse, unknown, ThrowOnError>({
        url: '/api/pages/{id}',
        ...options
    });
};

/**
 * Retrieve a page by ID
 */
export const findPageById = <ThrowOnError extends boolean = false>(options: Options<FindPageByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FindPageByIdResponse, unknown, ThrowOnError>({
        url: '/api/pages/{id}',
        ...options
    });
};

/**
 * Update a page by ID
 */
export const updatePageById = <ThrowOnError extends boolean = false>(options: Options<UpdatePageByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdatePageByIdResponse, unknown, ThrowOnError>({
        url: '/api/pages/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Count of pages
 */
export const countPages = <ThrowOnError extends boolean = false>(options?: Options<CountPagesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CountPagesResponse, unknown, ThrowOnError>({
        url: '/api/pages/count',
        ...options
    });
};

/**
 * Delete posts
 */
export const deletePosts = <ThrowOnError extends boolean = false>(options?: Options<DeletePostsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeletePostsResponse, unknown, ThrowOnError>({
        url: '/api/posts',
        ...options
    });
};

/**
 * List all posts
 */
export const findPosts = <ThrowOnError extends boolean = false>(options?: Options<FindPostsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<FindPostsResponse, unknown, ThrowOnError>({
        url: '/api/posts',
        ...options
    });
};

/**
 * Update posts
 */
export const updatePosts = <ThrowOnError extends boolean = false>(options?: Options<UpdatePostsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<UpdatePostsResponse, unknown, ThrowOnError>({
        url: '/api/posts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Create a new post
 */
export const createPost = <ThrowOnError extends boolean = false>(options?: Options<CreatePostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CreatePostResponse, unknown, ThrowOnError>({
        url: '/api/posts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a post by ID
 */
export const deletePostById = <ThrowOnError extends boolean = false>(options: Options<DeletePostByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeletePostByIdResponse, unknown, ThrowOnError>({
        url: '/api/posts/{id}',
        ...options
    });
};

/**
 * Retrieve a post by ID
 */
export const findPostById = <ThrowOnError extends boolean = false>(options: Options<FindPostByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FindPostByIdResponse, unknown, ThrowOnError>({
        url: '/api/posts/{id}',
        ...options
    });
};

/**
 * Update a post by ID
 */
export const updatePostById = <ThrowOnError extends boolean = false>(options: Options<UpdatePostByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdatePostByIdResponse, unknown, ThrowOnError>({
        url: '/api/posts/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Count of posts
 */
export const countPosts = <ThrowOnError extends boolean = false>(options?: Options<CountPostsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CountPostsResponse, unknown, ThrowOnError>({
        url: '/api/posts/count',
        ...options
    });
};

/**
 * Delete media
 */
export const deleteMedia = <ThrowOnError extends boolean = false>(options?: Options<DeleteMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteMediaResponse, unknown, ThrowOnError>({
        url: '/api/media',
        ...options
    });
};

/**
 * List all media
 */
export const findMedia = <ThrowOnError extends boolean = false>(options?: Options<FindMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<FindMediaResponse, unknown, ThrowOnError>({
        url: '/api/media',
        ...options
    });
};

/**
 * Update media
 */
export const updateMedia = <ThrowOnError extends boolean = false>(options?: Options<UpdateMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<UpdateMediaResponse, unknown, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/media',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

/**
 * Create a new media
 */
export const createMedia = <ThrowOnError extends boolean = false>(options?: Options<CreateMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CreateMediaResponse, unknown, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/media',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

/**
 * Delete a media by ID
 */
export const deleteMediaById = <ThrowOnError extends boolean = false>(options: Options<DeleteMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteMediaByIdResponse, unknown, ThrowOnError>({
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Retrieve a media by ID
 */
export const findMediaById = <ThrowOnError extends boolean = false>(options: Options<FindMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FindMediaByIdResponse, unknown, ThrowOnError>({
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Update a media by ID
 */
export const updateMediaById = <ThrowOnError extends boolean = false>(options: Options<UpdateMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateMediaByIdResponse, unknown, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/media/{id}',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

/**
 * Count of media
 */
export const countMedia = <ThrowOnError extends boolean = false>(options?: Options<CountMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CountMediaResponse, unknown, ThrowOnError>({
        url: '/api/media/count',
        ...options
    });
};

/**
 * Delete categories
 */
export const deleteCategories = <ThrowOnError extends boolean = false>(options?: Options<DeleteCategoriesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteCategoriesResponse, unknown, ThrowOnError>({
        url: '/api/categories',
        ...options
    });
};

/**
 * List all categories
 */
export const findCategories = <ThrowOnError extends boolean = false>(options?: Options<FindCategoriesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<FindCategoriesResponse, unknown, ThrowOnError>({
        url: '/api/categories',
        ...options
    });
};

/**
 * Update categories
 */
export const updateCategories = <ThrowOnError extends boolean = false>(options?: Options<UpdateCategoriesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<UpdateCategoriesResponse, unknown, ThrowOnError>({
        url: '/api/categories',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Create a new category
 */
export const createCategory = <ThrowOnError extends boolean = false>(options?: Options<CreateCategoryData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CreateCategoryResponse, unknown, ThrowOnError>({
        url: '/api/categories',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a category by ID
 */
export const deleteCategoryById = <ThrowOnError extends boolean = false>(options: Options<DeleteCategoryByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteCategoryByIdResponse, unknown, ThrowOnError>({
        url: '/api/categories/{id}',
        ...options
    });
};

/**
 * Retrieve a category by ID
 */
export const findCategoryById = <ThrowOnError extends boolean = false>(options: Options<FindCategoryByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FindCategoryByIdResponse, unknown, ThrowOnError>({
        url: '/api/categories/{id}',
        ...options
    });
};

/**
 * Update a category by ID
 */
export const updateCategoryById = <ThrowOnError extends boolean = false>(options: Options<UpdateCategoryByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateCategoryByIdResponse, unknown, ThrowOnError>({
        url: '/api/categories/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Count of categories
 */
export const countCategories = <ThrowOnError extends boolean = false>(options?: Options<CountCategoriesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CountCategoriesResponse, unknown, ThrowOnError>({
        url: '/api/categories/count',
        ...options
    });
};

/**
 * Delete users
 */
export const deleteUsers = <ThrowOnError extends boolean = false>(options?: Options<DeleteUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteUsersResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options
    });
};

/**
 * List all users
 */
export const findUsers = <ThrowOnError extends boolean = false>(options?: Options<FindUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<FindUsersResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options
    });
};

/**
 * Update users
 */
export const updateUsers = <ThrowOnError extends boolean = false>(options?: Options<UpdateUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).patch<UpdateUsersResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Create a new user
 */
export const createUser = <ThrowOnError extends boolean = false>(options?: Options<CreateUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CreateUserResponse, unknown, ThrowOnError>({
        url: '/api/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a user by ID
 */
export const deleteUserById = <ThrowOnError extends boolean = false>(options: Options<DeleteUserByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteUserByIdResponse, unknown, ThrowOnError>({
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Retrieve a user by ID
 */
export const findUserById = <ThrowOnError extends boolean = false>(options: Options<FindUserByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FindUserByIdResponse, unknown, ThrowOnError>({
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Update a user by ID
 */
export const updateUserById = <ThrowOnError extends boolean = false>(options: Options<UpdateUserByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<UpdateUserByIdResponse, unknown, ThrowOnError>({
        url: '/api/users/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Count of users
 */
export const countUsers = <ThrowOnError extends boolean = false>(options?: Options<CountUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CountUsersResponse, unknown, ThrowOnError>({
        url: '/api/users/count',
        ...options
    });
};

/**
 * Login
 */
export const login = <ThrowOnError extends boolean = false>(options?: Options<LoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<LoginResponse, unknown, ThrowOnError>({
        url: '/api/users/login',
        ...options
    });
};

/**
 * Logout
 */
export const logout = <ThrowOnError extends boolean = false>(options?: Options<LogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<LogoutResponse, unknown, ThrowOnError>({
        url: '/api/users/logout',
        ...options
    });
};

/**
 * Unlock
 */
export const unlock = <ThrowOnError extends boolean = false>(options?: Options<UnlockData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<UnlockResponse, unknown, ThrowOnError>({
        url: '/api/users/unlock',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Refresh token
 */
export const refreshToken = <ThrowOnError extends boolean = false>(options?: Options<RefreshTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<RefreshTokenResponse, unknown, ThrowOnError>({
        url: '/api/users/refresh-token',
        ...options
    });
};

/**
 * Current user
 */
export const currentUser = <ThrowOnError extends boolean = false>(options?: Options<CurrentUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<CurrentUserResponse, unknown, ThrowOnError>({
        url: '/api/users/me',
        ...options
    });
};

/**
 * Forgot password
 */
export const forgotPassword = <ThrowOnError extends boolean = false>(options?: Options<ForgotPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<ForgotPasswordResponse, unknown, ThrowOnError>({
        url: '/api/users/forgot-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Reset password
 */
export const resetPassword = <ThrowOnError extends boolean = false>(options?: Options<ResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<ResetPasswordResponse, unknown, ThrowOnError>({
        url: '/api/users/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Verify token
 */
export const verifyToken = <ThrowOnError extends boolean = false>(options?: Options<VerifyTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<VerifyTokenResponse, unknown, ThrowOnError>({
        url: '/api/users/verify/{token}',
        ...options
    });
};