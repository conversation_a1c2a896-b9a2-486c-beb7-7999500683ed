// This file is auto-generated by @hey-api/openapi-ts

export type Page = {
    id: number;
    updatedAt: string;
    createdAt: string;
    title: string;
    slug?: string | null;
    publishedAt?: string | null;
    _status?: 'draft' | 'published';
};

export type Post = {
    id: number;
    updatedAt: string;
    createdAt: string;
    title: string;
    slug?: string | null;
    publishedAt?: string | null;
    _status?: 'draft' | 'published';
};

export type Media = {
    id: number;
    updatedAt: string;
    createdAt: string;
    alt?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    url?: string | null;
};

export type Category = {
    id: number;
    updatedAt: string;
    createdAt: string;
    title: string;
    slug?: string | null;
};

export type User = {
    id: number;
    updatedAt: string;
    createdAt: string;
    name?: string | null;
    email: string;
    roles: Array<'subscriber' | 'editor' | 'admin' | 'super-admin'>;
};

export type DeletePagesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/pages';
};

export type DeletePagesResponses = {
    /**
     * pages deleted successfully
     */
    200: {
        message: string;
        deletedCount?: number;
    };
};

export type DeletePagesResponse = DeletePagesResponses[keyof DeletePagesResponses];

export type FindPagesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
        /**
         * Limit number of documents returned
         */
        limit?: number;
        /**
         * Page number for pagination
         */
        page?: number;
        /**
         * Field(s) to sort by
         */
        sort?: string;
        /**
         * Advanced filtering (JSON string)
         */
        where?: string;
    };
    url: '/api/pages';
};

export type FindPagesResponses = {
    /**
     * A list of pages
     */
    200: {
        docs: Array<Page>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage?: number | null;
        nextPage?: number | null;
    };
};

export type FindPagesResponse = FindPagesResponses[keyof FindPagesResponses];

export type UpdatePagesData = {
    body?: Page;
    path?: never;
    query?: never;
    url: '/api/pages';
};

export type UpdatePagesResponses = {
    /**
     * Updated pages
     */
    200: Page;
};

export type UpdatePagesResponse = UpdatePagesResponses[keyof UpdatePagesResponses];

export type CreatePageData = {
    body?: Page;
    path?: never;
    query?: never;
    url: '/api/pages';
};

export type CreatePageResponses = {
    /**
     * Created page
     */
    201: Page;
};

export type CreatePageResponse = CreatePageResponses[keyof CreatePageResponses];

export type DeletePageByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/pages/{id}';
};

export type DeletePageByIdResponses = {
    /**
     * page deleted successfully
     */
    200: {
        message: string;
        id: string;
    };
};

export type DeletePageByIdResponse = DeletePageByIdResponses[keyof DeletePageByIdResponses];

export type FindPageByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
    };
    url: '/api/pages/{id}';
};

export type FindPageByIdResponses = {
    /**
     * A single page
     */
    200: Page;
};

export type FindPageByIdResponse = FindPageByIdResponses[keyof FindPageByIdResponses];

export type UpdatePageByIdData = {
    body?: Page;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/pages/{id}';
};

export type UpdatePageByIdResponses = {
    /**
     * Updated page
     */
    200: Page;
};

export type UpdatePageByIdResponse = UpdatePageByIdResponses[keyof UpdatePageByIdResponses];

export type CountPagesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/pages/count';
};

export type CountPagesResponses = {
    /**
     * Count of pages
     */
    200: {
        totalDocs?: number;
    };
};

export type CountPagesResponse = CountPagesResponses[keyof CountPagesResponses];

export type DeletePostsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/posts';
};

export type DeletePostsResponses = {
    /**
     * posts deleted successfully
     */
    200: {
        message: string;
        deletedCount?: number;
    };
};

export type DeletePostsResponse = DeletePostsResponses[keyof DeletePostsResponses];

export type FindPostsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
        /**
         * Limit number of documents returned
         */
        limit?: number;
        /**
         * Page number for pagination
         */
        page?: number;
        /**
         * Field(s) to sort by
         */
        sort?: string;
        /**
         * Advanced filtering (JSON string)
         */
        where?: string;
    };
    url: '/api/posts';
};

export type FindPostsResponses = {
    /**
     * A list of posts
     */
    200: {
        docs: Array<Post>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage?: number | null;
        nextPage?: number | null;
    };
};

export type FindPostsResponse = FindPostsResponses[keyof FindPostsResponses];

export type UpdatePostsData = {
    body?: Post;
    path?: never;
    query?: never;
    url: '/api/posts';
};

export type UpdatePostsResponses = {
    /**
     * Updated posts
     */
    200: Post;
};

export type UpdatePostsResponse = UpdatePostsResponses[keyof UpdatePostsResponses];

export type CreatePostData = {
    body?: Post;
    path?: never;
    query?: never;
    url: '/api/posts';
};

export type CreatePostResponses = {
    /**
     * Created post
     */
    201: Post;
};

export type CreatePostResponse = CreatePostResponses[keyof CreatePostResponses];

export type DeletePostByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/posts/{id}';
};

export type DeletePostByIdResponses = {
    /**
     * post deleted successfully
     */
    200: {
        message: string;
        id: string;
    };
};

export type DeletePostByIdResponse = DeletePostByIdResponses[keyof DeletePostByIdResponses];

export type FindPostByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
    };
    url: '/api/posts/{id}';
};

export type FindPostByIdResponses = {
    /**
     * A single post
     */
    200: Post;
};

export type FindPostByIdResponse = FindPostByIdResponses[keyof FindPostByIdResponses];

export type UpdatePostByIdData = {
    body?: Post;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/posts/{id}';
};

export type UpdatePostByIdResponses = {
    /**
     * Updated post
     */
    200: Post;
};

export type UpdatePostByIdResponse = UpdatePostByIdResponses[keyof UpdatePostByIdResponses];

export type CountPostsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/posts/count';
};

export type CountPostsResponses = {
    /**
     * Count of posts
     */
    200: {
        totalDocs?: number;
    };
};

export type CountPostsResponse = CountPostsResponses[keyof CountPostsResponses];

export type DeleteMediaData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/media';
};

export type DeleteMediaResponses = {
    /**
     * media deleted successfully
     */
    200: {
        message: string;
        deletedCount?: number;
    };
};

export type DeleteMediaResponse = DeleteMediaResponses[keyof DeleteMediaResponses];

export type FindMediaData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
        /**
         * Limit number of documents returned
         */
        limit?: number;
        /**
         * Page number for pagination
         */
        page?: number;
        /**
         * Field(s) to sort by
         */
        sort?: string;
        /**
         * Advanced filtering (JSON string)
         */
        where?: string;
    };
    url: '/api/media';
};

export type FindMediaResponses = {
    /**
     * A list of media
     */
    200: {
        docs: Array<Media>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage?: number | null;
        nextPage?: number | null;
    };
};

export type FindMediaResponse = FindMediaResponses[keyof FindMediaResponses];

export type UpdateMediaData = {
    body?: {
        file?: Blob | File;
        /**
         * JSON-stringified object containing collection fields
         */
        _payload?: string;
    };
    path?: never;
    query?: never;
    url: '/api/media';
};

export type UpdateMediaResponses = {
    /**
     * Updated media
     */
    200: Media;
};

export type UpdateMediaResponse = UpdateMediaResponses[keyof UpdateMediaResponses];

export type CreateMediaData = {
    body?: {
        file: Blob | File;
        /**
         * JSON-stringified object containing collection fields
         */
        _payload?: string;
    };
    path?: never;
    query?: never;
    url: '/api/media';
};

export type CreateMediaResponses = {
    /**
     * Created media
     */
    201: Media;
};

export type CreateMediaResponse = CreateMediaResponses[keyof CreateMediaResponses];

export type DeleteMediaByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/media/{id}';
};

export type DeleteMediaByIdResponses = {
    /**
     * media deleted successfully
     */
    200: {
        message: string;
        id: string;
    };
};

export type DeleteMediaByIdResponse = DeleteMediaByIdResponses[keyof DeleteMediaByIdResponses];

export type FindMediaByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
    };
    url: '/api/media/{id}';
};

export type FindMediaByIdResponses = {
    /**
     * A single media
     */
    200: Media;
};

export type FindMediaByIdResponse = FindMediaByIdResponses[keyof FindMediaByIdResponses];

export type UpdateMediaByIdData = {
    body?: {
        file?: Blob | File;
        /**
         * JSON-stringified object containing collection fields
         */
        _payload?: string;
    };
    path: {
        id: string;
    };
    query?: never;
    url: '/api/media/{id}';
};

export type UpdateMediaByIdResponses = {
    /**
     * Updated media
     */
    200: Media;
};

export type UpdateMediaByIdResponse = UpdateMediaByIdResponses[keyof UpdateMediaByIdResponses];

export type CountMediaData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/media/count';
};

export type CountMediaResponses = {
    /**
     * Count of media
     */
    200: {
        totalDocs?: number;
    };
};

export type CountMediaResponse = CountMediaResponses[keyof CountMediaResponses];

export type DeleteCategoriesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/categories';
};

export type DeleteCategoriesResponses = {
    /**
     * categories deleted successfully
     */
    200: {
        message: string;
        deletedCount?: number;
    };
};

export type DeleteCategoriesResponse = DeleteCategoriesResponses[keyof DeleteCategoriesResponses];

export type FindCategoriesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
        /**
         * Limit number of documents returned
         */
        limit?: number;
        /**
         * Page number for pagination
         */
        page?: number;
        /**
         * Field(s) to sort by
         */
        sort?: string;
        /**
         * Advanced filtering (JSON string)
         */
        where?: string;
    };
    url: '/api/categories';
};

export type FindCategoriesResponses = {
    /**
     * A list of categories
     */
    200: {
        docs: Array<Category>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage?: number | null;
        nextPage?: number | null;
    };
};

export type FindCategoriesResponse = FindCategoriesResponses[keyof FindCategoriesResponses];

export type UpdateCategoriesData = {
    body?: Category;
    path?: never;
    query?: never;
    url: '/api/categories';
};

export type UpdateCategoriesResponses = {
    /**
     * Updated categories
     */
    200: Category;
};

export type UpdateCategoriesResponse = UpdateCategoriesResponses[keyof UpdateCategoriesResponses];

export type CreateCategoryData = {
    body?: Category;
    path?: never;
    query?: never;
    url: '/api/categories';
};

export type CreateCategoryResponses = {
    /**
     * Created category
     */
    201: Category;
};

export type CreateCategoryResponse = CreateCategoryResponses[keyof CreateCategoryResponses];

export type DeleteCategoryByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/categories/{id}';
};

export type DeleteCategoryByIdResponses = {
    /**
     * category deleted successfully
     */
    200: {
        message: string;
        id: string;
    };
};

export type DeleteCategoryByIdResponse = DeleteCategoryByIdResponses[keyof DeleteCategoryByIdResponses];

export type FindCategoryByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
    };
    url: '/api/categories/{id}';
};

export type FindCategoryByIdResponses = {
    /**
     * A single category
     */
    200: Category;
};

export type FindCategoryByIdResponse = FindCategoryByIdResponses[keyof FindCategoryByIdResponses];

export type UpdateCategoryByIdData = {
    body?: Category;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/categories/{id}';
};

export type UpdateCategoryByIdResponses = {
    /**
     * Updated category
     */
    200: Category;
};

export type UpdateCategoryByIdResponse = UpdateCategoryByIdResponses[keyof UpdateCategoryByIdResponses];

export type CountCategoriesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/categories/count';
};

export type CountCategoriesResponses = {
    /**
     * Count of categories
     */
    200: {
        totalDocs?: number;
    };
};

export type CountCategoriesResponse = CountCategoriesResponses[keyof CountCategoriesResponses];

export type DeleteUsersData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type DeleteUsersResponses = {
    /**
     * users deleted successfully
     */
    200: {
        message: string;
        deletedCount?: number;
    };
};

export type DeleteUsersResponse = DeleteUsersResponses[keyof DeleteUsersResponses];

export type FindUsersData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
        /**
         * Limit number of documents returned
         */
        limit?: number;
        /**
         * Page number for pagination
         */
        page?: number;
        /**
         * Field(s) to sort by
         */
        sort?: string;
        /**
         * Advanced filtering (JSON string)
         */
        where?: string;
    };
    url: '/api/users';
};

export type FindUsersResponses = {
    /**
     * A list of users
     */
    200: {
        docs: Array<User>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage?: number | null;
        nextPage?: number | null;
    };
};

export type FindUsersResponse = FindUsersResponses[keyof FindUsersResponses];

export type UpdateUsersData = {
    body?: User;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type UpdateUsersResponses = {
    /**
     * Updated users
     */
    200: User;
};

export type UpdateUsersResponse = UpdateUsersResponses[keyof UpdateUsersResponses];

export type CreateUserData = {
    body?: User;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type CreateUserResponses = {
    /**
     * Created user
     */
    201: User;
};

export type CreateUserResponse = CreateUserResponses[keyof CreateUserResponses];

export type DeleteUserByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/users/{id}';
};

export type DeleteUserByIdResponses = {
    /**
     * user deleted successfully
     */
    200: {
        message: string;
        id: string;
    };
};

export type DeleteUserByIdResponse = DeleteUserByIdResponses[keyof DeleteUserByIdResponses];

export type FindUserByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        /**
         * Auto-populate relationships and uploads
         */
        depth?: number;
    };
    url: '/api/users/{id}';
};

export type FindUserByIdResponses = {
    /**
     * A single user
     */
    200: User;
};

export type FindUserByIdResponse = FindUserByIdResponses[keyof FindUserByIdResponses];

export type UpdateUserByIdData = {
    body?: User;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/users/{id}';
};

export type UpdateUserByIdResponses = {
    /**
     * Updated user
     */
    200: User;
};

export type UpdateUserByIdResponse = UpdateUserByIdResponses[keyof UpdateUserByIdResponses];

export type CountUsersData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/count';
};

export type CountUsersResponses = {
    /**
     * Count of users
     */
    200: {
        totalDocs?: number;
    };
};

export type CountUsersResponse = CountUsersResponses[keyof CountUsersResponses];

export type LoginData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/login';
};

export type LoginResponses = {
    /**
     * Login
     */
    200: {
        [key: string]: unknown;
    };
};

export type LoginResponse = LoginResponses[keyof LoginResponses];

export type LogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/logout';
};

export type LogoutResponses = {
    /**
     * Logout
     */
    200: {
        [key: string]: unknown;
    };
};

export type LogoutResponse = LogoutResponses[keyof LogoutResponses];

export type UnlockData = {
    body?: {
        email?: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/unlock';
};

export type UnlockResponses = {
    /**
     * Unlock
     */
    200: {
        [key: string]: unknown;
    };
};

export type UnlockResponse = UnlockResponses[keyof UnlockResponses];

export type RefreshTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/refresh-token';
};

export type RefreshTokenResponses = {
    /**
     * Refresh token
     */
    200: {
        [key: string]: unknown;
    };
};

export type RefreshTokenResponse = RefreshTokenResponses[keyof RefreshTokenResponses];

export type CurrentUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/me';
};

export type CurrentUserResponses = {
    /**
     * Current user
     */
    200: {
        [key: string]: unknown;
    };
};

export type CurrentUserResponse = CurrentUserResponses[keyof CurrentUserResponses];

export type ForgotPasswordData = {
    body?: {
        email?: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/forgot-password';
};

export type ForgotPasswordResponses = {
    /**
     * Forgot password
     */
    200: {
        [key: string]: unknown;
    };
};

export type ForgotPasswordResponse = ForgotPasswordResponses[keyof ForgotPasswordResponses];

export type ResetPasswordData = {
    body?: {
        token?: string;
        password?: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/reset-password';
};

export type ResetPasswordResponses = {
    /**
     * Reset password
     */
    200: {
        [key: string]: unknown;
    };
};

export type ResetPasswordResponse = ResetPasswordResponses[keyof ResetPasswordResponses];

export type VerifyTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/verify/{token}';
};

export type VerifyTokenResponses = {
    /**
     * Verify token
     */
    200: {
        [key: string]: unknown;
    };
};

export type VerifyTokenResponse = VerifyTokenResponses[keyof VerifyTokenResponses];

export type ClientOptions = {
    baseUrl: `${string}://openapi.json` | (string & {});
};