// This file is auto-generated by @hey-api/openapi-ts

export type DeleteUsersData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type DeleteUsersResponses = {
    /**
     * users deleted successfully
     */
    204: void;
};

export type DeleteUsersResponse = DeleteUsersResponses[keyof DeleteUsersResponses];

export type FindUsersData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type FindUsersResponses = {
    /**
     * A list of users
     */
    200: {
        docs?: Array<unknown>;
        totalDocs?: number;
        limit?: number;
        totalPages?: number;
        page?: number;
        pagingCounter?: number;
        hasPrevPage?: boolean;
        hasNextPage?: boolean;
        prevPage?: number | null;
        nextPage?: number | null;
    };
};

export type FindUsersResponse = FindUsersResponses[keyof FindUsersResponses];

export type UpdateUsersData = {
    body?: {
        [key: string]: unknown;
    };
    path?: never;
    query?: never;
    url: '/api/users';
};

export type UpdateUsersResponses = {
    /**
     * Updated users
     */
    200: {
        [key: string]: unknown;
    };
};

export type UpdateUsersResponse = UpdateUsersResponses[keyof UpdateUsersResponses];

export type CreateUserData = {
    body?: {
        [key: string]: unknown;
    };
    path?: never;
    query?: never;
    url: '/api/users';
};

export type CreateUserResponses = {
    /**
     * Created user
     */
    201: {
        [key: string]: unknown;
    };
};

export type CreateUserResponse = CreateUserResponses[keyof CreateUserResponses];

export type DeleteUserByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/users/{id}';
};

export type DeleteUserByIdResponses = {
    /**
     * user deleted
     */
    204: void;
};

export type DeleteUserByIdResponse = DeleteUserByIdResponses[keyof DeleteUserByIdResponses];

export type FindUserByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/users/{id}';
};

export type FindUserByIdResponses = {
    /**
     * A single user
     */
    200: {
        [key: string]: unknown;
    };
};

export type FindUserByIdResponse = FindUserByIdResponses[keyof FindUserByIdResponses];

export type UpdateUserByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
    query?: never;
    url: '/api/users/{id}';
};

export type UpdateUserByIdResponses = {
    /**
     * Updated user
     */
    200: {
        [key: string]: unknown;
    };
};

export type UpdateUserByIdResponse = UpdateUserByIdResponses[keyof UpdateUserByIdResponses];

export type CountUsersData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/count';
};

export type CountUsersResponses = {
    /**
     * Count of users
     */
    200: {
        totalDocs?: number;
    };
};

export type CountUsersResponse = CountUsersResponses[keyof CountUsersResponses];

export type LoginData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/login';
};

export type LoginResponses = {
    /**
     * Login
     */
    200: {
        [key: string]: unknown;
    };
};

export type LoginResponse = LoginResponses[keyof LoginResponses];

export type LogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/logout';
};

export type LogoutResponses = {
    /**
     * Logout
     */
    200: {
        [key: string]: unknown;
    };
};

export type LogoutResponse = LogoutResponses[keyof LogoutResponses];

export type UnlockData = {
    body?: {
        email?: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/unlock';
};

export type UnlockResponses = {
    /**
     * Unlock
     */
    200: {
        [key: string]: unknown;
    };
};

export type UnlockResponse = UnlockResponses[keyof UnlockResponses];

export type RefreshTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/refresh-token';
};

export type RefreshTokenResponses = {
    /**
     * Refresh token
     */
    200: {
        [key: string]: unknown;
    };
};

export type RefreshTokenResponse = RefreshTokenResponses[keyof RefreshTokenResponses];

export type CurrentUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/me';
};

export type CurrentUserResponses = {
    /**
     * Current user
     */
    200: {
        [key: string]: unknown;
    };
};

export type CurrentUserResponse = CurrentUserResponses[keyof CurrentUserResponses];

export type ForgotPasswordData = {
    body?: {
        email?: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/forgot-password';
};

export type ForgotPasswordResponses = {
    /**
     * Forgot password
     */
    200: {
        [key: string]: unknown;
    };
};

export type ForgotPasswordResponse = ForgotPasswordResponses[keyof ForgotPasswordResponses];

export type ResetPasswordData = {
    body?: {
        token?: string;
        password?: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/reset-password';
};

export type ResetPasswordResponses = {
    /**
     * Reset password
     */
    200: {
        [key: string]: unknown;
    };
};

export type ResetPasswordResponse = ResetPasswordResponses[keyof ResetPasswordResponses];

export type VerifyTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/verify/{token}';
};

export type VerifyTokenResponses = {
    /**
     * Verify token
     */
    200: {
        [key: string]: unknown;
    };
};

export type VerifyTokenResponse = VerifyTokenResponses[keyof VerifyTokenResponses];

export type ClientOptions = {
    baseUrl: `${string}://openapi.json` | (string & {});
};