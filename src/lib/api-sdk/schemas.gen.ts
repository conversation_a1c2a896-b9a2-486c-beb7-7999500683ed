// This file is auto-generated by @hey-api/openapi-ts

export const PageSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'number'
        },
        updatedAt: {
            type: 'string',
            format: 'date-time'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        title: {
            type: 'string'
        },
        slug: {
            type: 'string',
            nullable: true
        },
        publishedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        _status: {
            type: 'string',
            enum: ['draft', 'published'],
            nullable: true
        }
    },
    required: ['id', 'updatedAt', 'createdAt', 'title']
} as const;

export const PostSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'number'
        },
        updatedAt: {
            type: 'string',
            format: 'date-time'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        title: {
            type: 'string'
        },
        slug: {
            type: 'string',
            nullable: true
        },
        publishedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        _status: {
            type: 'string',
            enum: ['draft', 'published'],
            nullable: true
        }
    },
    required: ['id', 'updatedAt', 'createdAt', 'title']
} as const;

export const MediaSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'number'
        },
        updatedAt: {
            type: 'string',
            format: 'date-time'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        alt: {
            type: 'string',
            nullable: true
        },
        filename: {
            type: 'string',
            nullable: true
        },
        mimeType: {
            type: 'string',
            nullable: true
        },
        filesize: {
            type: 'number',
            nullable: true
        },
        width: {
            type: 'number',
            nullable: true
        },
        height: {
            type: 'number',
            nullable: true
        },
        url: {
            type: 'string',
            nullable: true
        }
    },
    required: ['id', 'updatedAt', 'createdAt']
} as const;

export const CategorySchema = {
    type: 'object',
    properties: {
        id: {
            type: 'number'
        },
        updatedAt: {
            type: 'string',
            format: 'date-time'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        title: {
            type: 'string'
        },
        slug: {
            type: 'string',
            nullable: true
        }
    },
    required: ['id', 'updatedAt', 'createdAt', 'title']
} as const;

export const UserSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'number'
        },
        updatedAt: {
            type: 'string',
            format: 'date-time'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        name: {
            type: 'string',
            nullable: true
        },
        email: {
            type: 'string',
            format: 'email'
        },
        roles: {
            type: 'array',
            items: {
                type: 'string',
                enum: ['subscriber', 'editor', 'admin', 'super-admin']
            }
        }
    },
    required: ['id', 'updatedAt', 'createdAt', 'email', 'roles']
} as const;