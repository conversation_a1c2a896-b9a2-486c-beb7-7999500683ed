/**
 * API Client Runtime Configuration
 *
 * This file configures the fetch API client.
 * It sets up base URLs, default headers, and other client configuration.
 */
import { getClientSideUrl } from '@/utils'
import { client } from './api-sdk/client.gen'

// Server-side URL function (inline to avoid import issues)
const getServerSideURL = () => {
  let url = process.env.NEXT_PUBLIC_SERVER_URL

  if (!url && process.env.VERCEL_PROJECT_PRODUCTION_URL) {
    return `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  }

  if (!url) {
    url = 'https://serplens.local'
  }
  return url
}

// Configure the client
const isServer = typeof window === 'undefined'
const baseUrl = isServer ? getServerSideURL() : getClientSideUrl()

client.setConfig({
  baseUrl,
  headers: {
    'x-serplens': 'true'
  }
})
