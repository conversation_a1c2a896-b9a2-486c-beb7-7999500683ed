/**
 * API Client Runtime Configuration
 *
 * This file configures the Next.js API client using the Runtime API pattern.
 * It sets up base URLs, default headers, and other client configuration
 * that gets applied before the client is initialized.
 *
 * @see https://heyapi.dev/openapi-ts/clients/next-js#runtime-api
 */
import { getClientSideURL, getServerSideURL } from '@/utils'
import type { CreateClientConfig } from './api-sdk/client.gen'

export const createClientConfig: CreateClientConfig = (config) => {
  const isServer = typeof window === 'undefined'

  // Configure base URL for both server and client
  const baseUrl = isServer ? getServerSideURL() : getClientSideURL()

  return {
    ...config,
    baseUrl,
    headers: {
      'x-serplens': 'true',
      ...(config?.headers || {})
    }
  }
}
