import type { CollectionA<PERSON>ReadHook } from 'payload'
import { User } from 'src/payload-types'

// The `user` collection has access control locked so that users are not publicly accessible
// This means that we need to populate the authors manually here to protect user privacy
// GraphQL will not return mutated user data that differs from the underlying schema
// So we use an alternative `populatedAuthors` field to populate the user data, hidden from the admin UI
/* eslint-disable @typescript-eslint/no-unused-vars */
export const populateAuthors: <AUTHORS>
  doc,
  req,
  req: { payload }
}) => {
  if (doc?.authors && doc?.authors?.length > 0) {
    const authorDocs: User[] = []

    for (const author of doc.authors) {
      try {
        const authorDocument = await payload.findByID({
          id: typeof author === 'object' ? author?.id : author,
          collection: 'users',
          depth: 0
        })

        if (authorDocument) {
          authorDocs.push(authorDocument)
        }

        if (authorDocs.length > 0) {
          doc.populatedAuthors = authorDocs.map((authorDocument) => ({
            id: authorDocument.id,
            name: authorDocument.name
          }))
        }
      } catch {
        // swallow error
      }
    }
  }

  return doc
}
