import path from 'path'
import { PayloadRequest, buildConfig } from 'payload'
import sharp from 'sharp'
import { fileURLToPath } from 'url'
import { categories, media, pages, posts, users } from '@/collections'
import { defaultLexical } from '@/fields/defaultLexical'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { resendAdapter } from '@payloadcms/email-resend'
import { footer } from './collections/footer'
import { header } from './collections/header'
import { plugins } from './plugins/plugins'
import { getServerSideURL } from './utils/getUrl'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    importMap: {
      baseDir: path.resolve(dirname)
    },
    user: users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900
        }
      ]
    }
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.POSTGRES_URL || ''
    }
  }),
  collections: [pages, posts, media, categories, users],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [header, footer],
  plugins: [...plugins],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts')
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      }
    },
    tasks: []
  },
  email: resendAdapter({
    defaultFromAddress: process.env.FROM_ADDRESS || '',
    defaultFromName: process.env.FROM_NAME || '',
    apiKey: process.env.RESEND_API_KEY || ''
  })
})
