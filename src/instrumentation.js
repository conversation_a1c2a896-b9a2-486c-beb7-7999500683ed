export async function register() {
  // Don't try to convert this to a var (https://stackoverflow.com/questions/77427874/next-js-instrumentation-behaves-differently-with-conditional-extracted-to-variab)
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Reminder don't try to use checkEnv. See above
    if (process.env.NODE_ENV === 'development') {
      // Generate our OpenAPI 3 JSON file only
      // API client generation will be handled by a separate process to avoid webpack issues
      const { generateApi } = await import('@/scripts/generateApi')
      await generateApi()

      console.log('✅ OpenAPI specification generated successfully!')
      console.log('🔧 API client generation will be triggered automatically...')
    }

    // init the logger
    await import('pino')
    await import('next-logger')
  }
}
