export async function register() {
  // Don't try to convert this to a var (https://stackoverflow.com/questions/77427874/next-js-instrumentation-behaves-differently-with-conditional-extracted-to-variab)
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Reminder don't try to use checkEnv. See above
    if (process.env.NODE_ENV === 'development') {
      // Generate our OpenAPI 3 JSON file and API client.
      // This will need to be regenerated when Payload collections are changed (collection added/removed).
      // openapi.json and the api-sdk folder should be committed to the repo!
      const { generateApi } = await import('@/scripts/generateApi')
      await generateApi()

      // ESModules didn't work here for some reason
      const { createClient } = require('@hey-api/openapi-ts') // eslint-disable-line @typescript-eslint/no-require-imports

      await createClient({
        input: 'openapi.json',
        output: {
          format: 'prettier',
          lint: 'eslint',
          path: './src/lib/api-sdk'
        },
        plugins: [
          {
            name: '@hey-api/client-next',
            runtimeConfigPath: './src/lib/clientConfig'
          },
          '@tanstack/react-query'
        ]
      })
    }
  }
}
