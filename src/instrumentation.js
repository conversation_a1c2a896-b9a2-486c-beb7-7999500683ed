export async function register() {
  // Don't try to convert this to a var (https://stackoverflow.com/questions/77427874/next-js-instrumentation-behaves-differently-with-conditional-extracted-to-variab)
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Reminder don't try to use checkEnv. See above
    if (process.env.NODE_ENV === 'development') {
      // Generate our OpenAPI 3 JSON file only.
      // This will need to be regenerated when Payload collections are changed (collection added/removed).
      // openapi.json should be committed to the repo!
      const { generateApi } = await import('@/scripts/generateApi')
      await generateApi()

      console.log('✅ OpenAPI specification generated successfully!')

      // Generate API client using spawn to avoid ESM issues
      try {
        const { spawn } = await import('child_process')

        console.log('🔧 Generating API client with Hey API and TanStack Query...')

        const clientProcess = spawn('pnpm', ['generate:api'], {
          stdio: 'pipe',
          shell: true
        })

        let output = ''
        let errorOutput = ''

        clientProcess.stdout?.on('data', (data) => {
          output += data.toString()
        })

        clientProcess.stderr?.on('data', (data) => {
          errorOutput += data.toString()
        })

        const exitCode = await new Promise((resolve) => {
          clientProcess.on('close', resolve)
        })

        if (exitCode === 0) {
          console.log('✅ API client generated successfully!')
        } else {
          console.log('⚠️  API client generation had issues, but continuing...')
          if (errorOutput) {
            console.log('Error details:', errorOutput)
          }
        }
      } catch (error) {
        console.log('⚠️  Could not generate API client:', error.message)
        console.log('💡 You can run "pnpm generate:api" manually after server starts')
      }
    }
  }
}
