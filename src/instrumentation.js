import { writeFile } from 'fs/promises'

export async function register() {
  // Don't try to convert this to a var (https://stackoverflow.com/questions/77427874/next-js-instrumentation-behaves-differently-with-conditional-extracted-to-variab)
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Reminder don't try to use checkEnv. See above
    if (process.env.NODE_ENV === 'development') {
      try {
        console.log('🔧 Fetching OpenAPI specification from payload-oapi plugin...')
        
        // Wait a bit for the server to be fully ready
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Fetch OpenAPI spec from payload-oapi plugin
        const response = await fetch('http://localhost:3000/api/openapi.json')
        
        if (!response.ok) {
          throw new Error(`Failed to fetch OpenAPI spec: ${response.status} ${response.statusText}`)
        }
        
        const spec = await response.json()
        
        // Save OpenAPI spec to file
        await writeFile('openapi.json', JSON.stringify(spec, null, 2))
        console.log('✅ OpenAPI specification saved to openapi.json')
        
        // Generate API client using Hey API
        const { createClient } = require('@hey-api/openapi-ts') // eslint-disable-line unicorn/prefer-module
        
        console.log('🔧 Generating API client with Hey API and TanStack Query...')
        
        await createClient({
          client: '@hey-api/client-next',
          input: 'openapi.json',
          output: 'src/lib/api-sdk',
          plugins: [
            '@hey-api/schemas',
            {
              enums: 'javascript',
              name: '@hey-api/typescript'
            },
            '@hey-api/sdk',
            '@tanstack/react-query'
          ]
        })
        
        console.log('✅ API client generated successfully in src/lib/api-sdk')
        
      } catch (error) {
        console.error('❌ Error in API generation process:', error.message)
        console.error('The application will continue without API client generation.')
        
        // Don't throw the error - let the app continue
        // This prevents the entire app from failing if API generation fails
      }
    }

    // init the logger
    await import('pino')
    await import('next-logger')
  }
}
