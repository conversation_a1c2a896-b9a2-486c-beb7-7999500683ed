import React from 'react'
import type { StaticImageData } from 'next/image'
import { RichText } from '@/components/RichText'
import { Media } from '@/components/media'
import type { MediaBlock as MediaBlockProperties } from '@/payload-types'
import { cn } from '@/utils'

type Properties = MediaBlockProperties & {
  breakout?: boolean
  captionClassName?: string
  className?: string
  enableGutter?: boolean
  imgClassName?: string
  staticImage?: StaticImageData
  disableInnerContainer?: boolean
}

export const MediaBlock: React.FC<Properties> = (properties) => {
  const {
    captionClassName,
    className,
    enableGutter = true,
    imgClassName,
    media,
    staticImage,
    disableInnerContainer
  } = properties

  let caption
  if (media && typeof media === 'object') caption = media.caption

  return (
    <div
      className={cn(
        '',
        {
          container: enableGutter
        },
        className
      )}>
      {(media || staticImage) && (
        <Media
          imgClassName={cn('border border-border rounded-[0.8rem]', imgClassName)}
          resource={media}
          src={staticImage}
        />
      )}
      {caption && (
        <div
          className={cn(
            'mt-6',
            {
              container: !disableInnerContainer
            },
            captionClassName
          )}>
          <RichText data={caption} enableGutter={false} />
        </div>
      )}
    </div>
  )
}
