import React, { Fragment } from 'react'
import { ArchiveBlock } from '@/blocks/ArchiveBlock'
import { CallToActionBlock } from '@/blocks/CallToAction'
import { ContentBlock } from '@/blocks/Content'
import { MediaBlock } from '@/blocks/MediaBlock'
import { FormBlock } from '@/blocks/form/FormBlock'
import type { Page } from '@/payload-types'

const blockComponents = {
  archive: ArchiveBlock,
  content: ContentBlock,
  cta: CallToActionBlock,
  formBlock: FormBlock,
  mediaBlock: MediaBlock
}

export const RenderBlocks: React.FC<{
  blocks: Page['layout'][0][]
}> = (properties) => {
  const { blocks } = properties

  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    return (
      <Fragment>
        {blocks.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              return (
                <div className='my-16' key={index}>
                  {/* @ts-expect-error there may be some mismatch between the expected types here */}
                  <Block {...block} disableInnerContainer />
                </div>
              )
            }
          }
          return null
        })}
      </Fragment>
    )
  }

  return null
}
