'use client'

import React from 'react'
import { Highlight, themes } from 'prism-react-renderer'
import { CopyButton } from './CopyButton'

type Properties = {
  code: string
  language?: string
}

export const Code: React.FC<Properties> = ({ code, language = '' }) => {
  if (!code) return null

  return (
    <Highlight code={code} language={language} theme={themes.vsDark}>
      {({ getLineProps, getTokenProps, tokens }) => (
        <pre className='bg-black p-4 border text-xs border-border rounded overflow-x-auto'>
          {tokens.map((line, index) => (
            <div key={index} {...getLineProps({ className: 'table-row', line })}>
              <span className='table-cell select-none text-right text-white/25'>
                {index + 1}
              </span>
              <span className='table-cell pl-4'>
                {line.map((token, key) => (
                  <span key={key} {...getTokenProps({ token })} />
                ))}
              </span>
            </div>
          ))}
          <CopyButton code={code} />
        </pre>
      )}
    </Highlight>
  )
}
