import React from 'react'
import { Code } from './CodeClient'

export type CodeBlockProps = {
  code: string
  language?: string
  blockType: 'code'
}

type Properties = CodeBlockProps & {
  className?: string
}

export const CodeBlock: React.FC<Properties> = ({ className, code, language }) => {
  return (
    <div className={[className, 'not-prose'].filter(Boolean).join(' ')}>
      <Code code={code} language={language} />
    </div>
  )
}
