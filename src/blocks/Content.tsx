import React from 'react'
import { CMSLink } from '@/components/Link'
import { RichText } from '@/components/RichText'
import type { ContentBlock as ContentBlockProperties } from '@/payload-types'
import { cn } from '@/utils'

export const ContentBlock: React.FC<ContentBlockProperties> = (properties) => {
  const { columns } = properties

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneThird: '4',
    twoThirds: '8'
  }

  return (
    <div className='container my-16'>
      <div className='grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-16'>
        {columns &&
          columns.length > 0 &&
          columns.map((col, index) => {
            const { enableLink, link, richText, size } = col

            return (
              <div
                className={cn(`col-span-4 lg:col-span-${colsSpanClasses[size!]}`, {
                  'md:col-span-2': size !== 'full'
                })}
                key={index}>
                {richText && <RichText data={richText} enableGutter={false} />}

                {enableLink && <CMSLink {...link} />}
              </div>
            )
          })}
      </div>
    </div>
  )
}
