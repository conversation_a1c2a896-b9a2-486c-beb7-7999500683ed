import React from 'react'
import clsx from 'clsx'
import { Card } from '@/components/Card'
import { RichText } from '@/components/RichText'
import type { Post } from '@/payload-types'
import { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'

export type RelatedPostsProps = {
  className?: string
  docs?: Post[]
  introContent?: SerializedEditorState
}

export const RelatedPosts: React.FC<RelatedPostsProps> = (properties) => {
  const { className, docs, introContent } = properties

  return (
    <div className={clsx('lg:container', className)}>
      {introContent && <RichText data={introContent} enableGutter={false} />}

      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8 items-stretch'>
        {docs?.map((document_, index) => {
          if (typeof document_ === 'string') return null

          return <Card key={index} doc={document_} relationTo='posts' showCategories />
        })}
      </div>
    </div>
  )
}
