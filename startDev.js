import { spawn } from 'node:child_process'
import path from 'node:path'

let dockerProcess,
  devProcess,
  isShuttingDown = false

// Util: get mkcert root CA path dynamically (cross-platform)
const getMkcertRootCAPath = () => {
  return new Promise((resolve, reject) => {
    const proc = spawn('mkcert', ['-CAROOT'])

    let output = ''
    proc.stdout.on('data', (data) => {
      output += data.toString()
    })

    proc.stderr.on('data', (data) => {
      console.error('mkcert error:', data.toString())
    })

    proc.on('close', (code) => {
      if (code === 0) {
        const caroot = output.trim()
        resolve(path.join(caroot, 'rootCA.pem'))
      } else {
        reject(new Error(`mkcert exited with code ${code}`))
      }
    })
  })
}

// Note: API client generation is now optional and can be run manually:
// pnpm generate:openapi (fetch spec from running server)
// pnpm generate:api (generate client from spec)

const startDocker = () => {
  console.log('🖥️ Starting Docker containers...')

  dockerProcess = spawn(
    'docker-compose',
    ['up', '--no-log-prefix', '--remove-orphans', '--detach'],
    {
      stdio: 'inherit',
      detached: true
    }
  )

  dockerProcess.on('error', (error) => {
    console.error('Error starting Docker:', error)
    stopAll()
  })
}

const startNextDevServer = (caPath) => {
  console.log('🧑‍💻 Starting Next.js dev server...')

  console.log(`Using mkcert CA path: ${caPath}`)
  const quotedCaPath = `"${caPath}"`

  devProcess = spawn(
    'cross-env',
    [
      'NODE_OPTIONS=--no-deprecation',
      'next',
      'dev',
      '--experimental-https-ca',
      quotedCaPath,
      '--experimental-https',
      '--experimental-https-key',
      './nginx/certs/serplens.local.key.pem',
      '--experimental-https-cert',
      './nginx/certs/serplens.local.cert.pem'
    ],
    {
      stdio: 'inherit',
      detached: true,
      shell: true
    }
  )

  devProcess.on('exit', (code) => {
    console.log(`Next.js server exited with code ${code}`)
    devProcess = null // Mark as no longer running
    if (!isShuttingDown) {
      stopAll()
    }
    process.exit(code)
  })

  devProcess.on('error', (error) => {
    console.error('Error starting Next.js:', error)
    stopAll()
  })
}

const stopAll = () => {
  if (isShuttingDown) {
    return // Prevent multiple shutdown attempts
  }
  isShuttingDown = true

  console.log('✋ Stopping processes...')

  if (dockerProcess) {
    console.log('🖥️ Stopping Docker containers...')
    const stopDocker = spawn('docker-compose', ['down'], { stdio: 'inherit' })
    stopDocker.on('exit', () => {
      console.log('Docker containers stopped.')
    })
  }

  if (devProcess && !devProcess.killed) {
    console.log('🧑‍💻 Stopping NextJS dev server...')
    try {
      process.kill(-devProcess.pid, 'SIGINT')
    } catch (error) {
      if (error.code !== 'ESRCH') {
        console.error('Error stopping NextJS dev server:', error)
      }
    }
  }
}

process.on('SIGINT', stopAll)
process.on('SIGTERM', stopAll)

const main = async () => {
  try {
    // 1. Start Docker containers first
    startDocker()

    // 2. Start Next.js dev server (includes Payload with OpenAPI plugin)
    const caPath = await getMkcertRootCAPath()
    startNextDevServer(caPath)

    // Note: OpenAPI spec is now auto-generated by payload-oapi plugin
    // Available at: http://localhost:3000/api/openapi.json
    // Documentation UI at: http://localhost:3000/api/docs
    console.log('📚 API Documentation available at: https://serplens.local/api/docs')
  } catch (err) {
    console.error('❌ Failed to start:', err)
    stopAll()
  }
}

main()
