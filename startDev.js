import { spawn } from 'node:child_process'
import path from 'node:path'

let dockerProcess,
  devProcess,
  isShuttingDown = false

// Util: get mkcert root CA path dynamically (cross-platform)
const getMkcertRootCAPath = () => {
  return new Promise((resolve, reject) => {
    const proc = spawn('mkcert', ['-CAROOT'])

    let output = ''
    proc.stdout.on('data', (data) => {
      output += data.toString()
    })

    proc.stderr.on('data', (data) => {
      console.error('mkcert error:', data.toString())
    })

    proc.on('close', (code) => {
      if (code === 0) {
        const caroot = output.trim()
        resolve(path.join(caroot, 'rootCA.pem'))
      } else {
        reject(new Error(`mkcert exited with code ${code}`))
      }
    })
  })
}

// Generate API client using openapi-ts config
const generateApiClient = () => {
  return new Promise((resolve, reject) => {
    try {
      console.log('� Generating API client with Next.js and TanStack Query...')

      const clientProcess = spawn('pnpm', ['generate:api'], {
        stdio: 'inherit'
      })

      clientProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ API client generated successfully!')
          resolve()
        } else {
          console.error(`❌ API client generation failed with code ${code}`)
          console.error('The application will continue without API client generation.')
          resolve()
        }
      })

      clientProcess.on('error', (error) => {
        console.error('❌ Error generating API client:', error)
        console.error('The application will continue without API client generation.')
        resolve()
      })
    } catch (error) {
      console.error('❌ Error in API generation process:', error)
      console.error('The application will continue without API client generation.')
      resolve()
    }
  })
}

const startDocker = () => {
  console.log('🖥️ Starting Docker containers...')

  dockerProcess = spawn(
    'docker-compose',
    ['up', '--no-log-prefix', '--remove-orphans', '--detach'],
    {
      stdio: 'inherit',
      detached: true
    }
  )

  dockerProcess.on('error', (error) => {
    console.error('Error starting Docker:', error)
    stopAll()
  })
}

const startNextDevServer = (caPath) => {
  console.log('🧑‍💻 Starting Next.js dev server...')

  console.log(`Using mkcert CA path: ${caPath}`)
  const quotedCaPath = `"${caPath}"`

  devProcess = spawn(
    'cross-env',
    [
      'NODE_OPTIONS=--no-deprecation',
      'next',
      'dev',
      '--experimental-https-ca',
      quotedCaPath,
      '--experimental-https',
      '--experimental-https-key',
      './nginx/certs/serplens.local.key.pem',
      '--experimental-https-cert',
      './nginx/certs/serplens.local.cert.pem'
    ],
    {
      stdio: 'inherit',
      detached: true,
      shell: true
    }
  )

  devProcess.on('exit', (code) => {
    console.log(`Next.js server exited with code ${code}`)
    devProcess = null // Mark as no longer running
    if (!isShuttingDown) {
      stopAll()
    }
    process.exit(code)
  })

  devProcess.on('error', (error) => {
    console.error('Error starting Next.js:', error)
    stopAll()
  })
}

const stopAll = () => {
  if (isShuttingDown) {
    return // Prevent multiple shutdown attempts
  }
  isShuttingDown = true

  console.log('✋ Stopping processes...')

  if (dockerProcess) {
    console.log('🖥️ Stopping Docker containers...')
    const stopDocker = spawn('docker-compose', ['down'], { stdio: 'inherit' })
    stopDocker.on('exit', () => {
      console.log('Docker containers stopped.')
    })
  }

  if (devProcess && !devProcess.killed) {
    console.log('🧑‍💻 Stopping NextJS dev server...')
    try {
      process.kill(-devProcess.pid, 'SIGINT')
    } catch (error) {
      if (error.code !== 'ESRCH') {
        console.error('Error stopping NextJS dev server:', error)
      }
    }
  }
}

process.on('SIGINT', stopAll)
process.on('SIGTERM', stopAll)

// Generate OpenAPI specification from Payload collections
const generateOpenApiSpec = () => {
  return new Promise((resolve, reject) => {
    try {
      console.log('🔧 Generating OpenAPI specification from Payload collections...')

      const specProcess = spawn(
        'node',
        [
          '-e',
          `
        import('./src/scripts/generateApi.js').then(m => m.generateApi()).catch(console.error)
      `
        ],
        { stdio: 'inherit' }
      )

      specProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ OpenAPI specification generated successfully!')
          resolve()
        } else {
          reject(new Error(`OpenAPI generation failed with code: ${code}`))
        }
      })

      specProcess.on('error', (error) => {
        reject(new Error(`OpenAPI generation process error: ${error.message}`))
      })
    } catch (error) {
      reject(new Error(`Failed to start OpenAPI generation: ${error.message}`))
    }
  })
}

const main = async () => {
  try {
    // 1. Generate OpenAPI specification from Payload collections
    await generateOpenApiSpec()

    // 2. Generate API client from OpenAPI specification
    await generateApiClient()

    // 3. Start Docker containers
    startDocker()

    // 4. Start Next.js dev server
    const caPath = await getMkcertRootCAPath()
    startNextDevServer(caPath)
  } catch (err) {
    console.error('❌ Failed to start:', err)
    stopAll()
  }
}

main()
