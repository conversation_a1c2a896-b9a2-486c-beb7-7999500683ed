{"openapi": "3.0.3", "info": {"title": "SERP Lens API", "version": "1.0.0", "description": "Auto-generated OpenAPI specification for SERP Lens CMS API"}, "servers": [{"url": "https://localhost:3000"}], "paths": {"/api/pages": {"get": {"summary": "Retrieve a list of Pages", "tags": ["Pages"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["title", "-title", "publishedAt", "-publishedAt", "slug", "-slug", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/PageQueryOperations"}, {"$ref": "#/components/schemas/PageQueryOperationsAnd"}, {"$ref": "#/components/schemas/PageQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/PageListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Page", "tags": ["Pages"], "requestBody": {"$ref": "#/components/requestBodies/PageRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewPageResponse"}}, "security": [{"ApiKey": []}]}}, "/api/pages/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Page", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Page by ID", "tags": ["Pages"], "responses": {"200": {"$ref": "#/components/responses/PageResponse"}, "404": {"$ref": "#/components/responses/PageNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Page", "tags": ["Pages"], "responses": {"200": {"$ref": "#/components/responses/PageResponse"}, "404": {"$ref": "#/components/responses/PageNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Page", "tags": ["Pages"], "responses": {"200": {"$ref": "#/components/responses/PageResponse"}, "404": {"$ref": "#/components/responses/PageNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/posts": {"get": {"summary": "Retrieve a list of Posts", "tags": ["Posts"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["title", "-title", "publishedAt", "-publishedAt", "slug", "-slug", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/PostQueryOperations"}, {"$ref": "#/components/schemas/PostQueryOperationsAnd"}, {"$ref": "#/components/schemas/PostQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/PostListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Post", "tags": ["Posts"], "requestBody": {"$ref": "#/components/requestBodies/PostRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewPostResponse"}}, "security": [{"ApiKey": []}]}}, "/api/posts/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Post", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Post by ID", "tags": ["Posts"], "responses": {"200": {"$ref": "#/components/responses/PostResponse"}, "404": {"$ref": "#/components/responses/PostNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Post", "tags": ["Posts"], "responses": {"200": {"$ref": "#/components/responses/PostResponse"}, "404": {"$ref": "#/components/responses/PostNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Post", "tags": ["Posts"], "responses": {"200": {"$ref": "#/components/responses/PostResponse"}, "404": {"$ref": "#/components/responses/PostNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/media": {"get": {"summary": "Retrieve a list of Media", "tags": ["Media"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["alt", "-alt", "prefix", "-prefix", "updatedAt", "-updatedAt", "createdAt", "-createdAt", "url", "-url", "thumbnailURL", "-thumbnailURL", "filename", "-filename", "mimeType", "-mimeType", "filesize", "-filesize", "width", "-width", "height", "-height", "focalX", "-focalX", "focalY", "-focalY"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/MediaQueryOperations"}, {"$ref": "#/components/schemas/MediaQueryOperationsAnd"}, {"$ref": "#/components/schemas/MediaQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/MediaListResponse"}}, "security": []}, "post": {"summary": "Create a new Media", "tags": ["Media"], "requestBody": {"$ref": "#/components/requestBodies/MediaRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewMediaResponse"}}, "security": [{"ApiKey": []}]}}, "/api/media/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Media", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Media by ID", "tags": ["Media"], "responses": {"200": {"$ref": "#/components/responses/MediaResponse"}, "404": {"$ref": "#/components/responses/MediaNotFoundResponse"}}, "security": []}, "patch": {"summary": "Update a Media", "tags": ["Media"], "responses": {"200": {"$ref": "#/components/responses/MediaResponse"}, "404": {"$ref": "#/components/responses/MediaNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Media", "tags": ["Media"], "responses": {"200": {"$ref": "#/components/responses/MediaResponse"}, "404": {"$ref": "#/components/responses/MediaNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/categories": {"get": {"summary": "Retrieve a list of Categories", "tags": ["Categories"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["title", "-title", "slug", "-slug", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/CategoryQueryOperations"}, {"$ref": "#/components/schemas/CategoryQueryOperationsAnd"}, {"$ref": "#/components/schemas/CategoryQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/CategoryListResponse"}}, "security": []}, "post": {"summary": "Create a new Category", "tags": ["Categories"], "requestBody": {"$ref": "#/components/requestBodies/CategoryRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewCategoryResponse"}}, "security": [{"ApiKey": []}]}}, "/api/categories/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Category", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Category by ID", "tags": ["Categories"], "responses": {"200": {"$ref": "#/components/responses/CategoryResponse"}, "404": {"$ref": "#/components/responses/CategoryNotFoundResponse"}}, "security": []}, "patch": {"summary": "Update a Category", "tags": ["Categories"], "responses": {"200": {"$ref": "#/components/responses/CategoryResponse"}, "404": {"$ref": "#/components/responses/CategoryNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Category", "tags": ["Categories"], "responses": {"200": {"$ref": "#/components/responses/CategoryResponse"}, "404": {"$ref": "#/components/responses/CategoryNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/users": {"get": {"summary": "Retrieve a list of Users", "tags": ["Users"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["name", "-name", "updatedAt", "-updatedAt", "createdAt", "-createdAt", "email", "-email", "resetPasswordToken", "-resetPasswordToken", "resetPasswordExpiration", "-resetPasswordExpiration", "salt", "-salt", "hash", "-hash", "loginAttempts", "-loginAttempts", "lockUntil", "-lock<PERSON><PERSON><PERSON>"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/UserQueryOperations"}, {"$ref": "#/components/schemas/UserQueryOperationsAnd"}, {"$ref": "#/components/schemas/UserQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/UserListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new User", "tags": ["Users"], "requestBody": {"$ref": "#/components/requestBodies/UserRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewUserResponse"}}, "security": [{"ApiKey": []}]}}, "/api/users/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the User", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a User by ID", "tags": ["Users"], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "404": {"$ref": "#/components/responses/UserNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a User", "tags": ["Users"], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "404": {"$ref": "#/components/responses/UserNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a User", "tags": ["Users"], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "404": {"$ref": "#/components/responses/UserNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/redirects": {"get": {"summary": "Retrieve a list of Redirects", "tags": ["Redirects"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["from", "-from", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/RedirectQueryOperations"}, {"$ref": "#/components/schemas/RedirectQueryOperationsAnd"}, {"$ref": "#/components/schemas/RedirectQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/RedirectListResponse"}}, "security": []}, "post": {"summary": "Create a new Redirect", "tags": ["Redirects"], "requestBody": {"$ref": "#/components/requestBodies/RedirectRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewRedirectResponse"}}, "security": [{"ApiKey": []}]}}, "/api/redirects/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Redirect", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Redirect by ID", "tags": ["Redirects"], "responses": {"200": {"$ref": "#/components/responses/RedirectResponse"}, "404": {"$ref": "#/components/responses/RedirectNotFoundResponse"}}, "security": []}, "patch": {"summary": "Update a Redirect", "tags": ["Redirects"], "responses": {"200": {"$ref": "#/components/responses/RedirectResponse"}, "404": {"$ref": "#/components/responses/RedirectNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Redirect", "tags": ["Redirects"], "responses": {"200": {"$ref": "#/components/responses/RedirectResponse"}, "404": {"$ref": "#/components/responses/RedirectNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/forms": {"get": {"summary": "Retrieve a list of Forms", "tags": ["Forms"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["title", "-title", "submitButtonLabel", "-submit<PERSON>uttonLabel", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/FormQueryOperations"}, {"$ref": "#/components/schemas/FormQueryOperationsAnd"}, {"$ref": "#/components/schemas/FormQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/FormListResponse"}}, "security": []}, "post": {"summary": "Create a new Form", "tags": ["Forms"], "requestBody": {"$ref": "#/components/requestBodies/FormRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewFormResponse"}}, "security": [{"ApiKey": []}]}}, "/api/forms/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Form", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Form by ID", "tags": ["Forms"], "responses": {"200": {"$ref": "#/components/responses/FormResponse"}, "404": {"$ref": "#/components/responses/FormNotFoundResponse"}}, "security": []}, "patch": {"summary": "Update a Form", "tags": ["Forms"], "responses": {"200": {"$ref": "#/components/responses/FormResponse"}, "404": {"$ref": "#/components/responses/FormNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Form", "tags": ["Forms"], "responses": {"200": {"$ref": "#/components/responses/FormResponse"}, "404": {"$ref": "#/components/responses/FormNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/form-submissions": {"get": {"summary": "Retrieve a list of Form Submissions", "tags": ["Form Submissions"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/FormSubmissionQueryOperations"}, {"$ref": "#/components/schemas/FormSubmissionQueryOperationsAnd"}, {"$ref": "#/components/schemas/FormSubmissionQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/FormSubmissionListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Form Submission", "tags": ["Form Submissions"], "requestBody": {"$ref": "#/components/requestBodies/FormSubmissionRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewFormSubmissionResponse"}}, "security": []}}, "/api/form-submissions/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Form Submission", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Form Submission by ID", "tags": ["Form Submissions"], "responses": {"200": {"$ref": "#/components/responses/FormSubmissionResponse"}, "404": {"$ref": "#/components/responses/FormSubmissionNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Form Submission", "tags": ["Form Submissions"], "responses": {"200": {"$ref": "#/components/responses/FormSubmissionResponse"}, "404": {"$ref": "#/components/responses/FormSubmissionNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Form Submission", "tags": ["Form Submissions"], "responses": {"200": {"$ref": "#/components/responses/FormSubmissionResponse"}, "404": {"$ref": "#/components/responses/FormSubmissionNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/search": {"get": {"summary": "Retrieve a list of Search Results", "tags": ["Search Results"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["title", "-title", "priority", "-priority", "slug", "-slug", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/SearchResultQueryOperations"}, {"$ref": "#/components/schemas/SearchResultQueryOperationsAnd"}, {"$ref": "#/components/schemas/SearchResultQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/SearchResultListResponse"}}, "security": []}, "post": {"summary": "Create a new Search Result", "tags": ["Search Results"], "requestBody": {"$ref": "#/components/requestBodies/SearchResultRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewSearchResultResponse"}}, "security": [{"ApiKey": []}]}}, "/api/search/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Search Result", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Search Result by ID", "tags": ["Search Results"], "responses": {"200": {"$ref": "#/components/responses/SearchResultResponse"}, "404": {"$ref": "#/components/responses/SearchResultNotFoundResponse"}}, "security": []}, "patch": {"summary": "Update a Search Result", "tags": ["Search Results"], "responses": {"200": {"$ref": "#/components/responses/SearchResultResponse"}, "404": {"$ref": "#/components/responses/SearchResultNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Search Result", "tags": ["Search Results"], "responses": {"200": {"$ref": "#/components/responses/SearchResultResponse"}, "404": {"$ref": "#/components/responses/SearchResultNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-jobs": {"get": {"summary": "Retrieve a list of Payload Jobs", "tags": ["Payload Jobs"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["queue", "-queue", "waitUntil", "-wait<PERSON><PERSON>l", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/PayloadJobQueryOperations"}, {"$ref": "#/components/schemas/PayloadJobQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadJobQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/PayloadJobListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Payload Job", "tags": ["Payload Jobs"], "requestBody": {"$ref": "#/components/requestBodies/PayloadJobRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewPayloadJobResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-jobs/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Payload Job", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Payload Job by ID", "tags": ["Payload Jobs"], "responses": {"200": {"$ref": "#/components/responses/PayloadJobResponse"}, "404": {"$ref": "#/components/responses/PayloadJobNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Payload Job", "tags": ["Payload Jobs"], "responses": {"200": {"$ref": "#/components/responses/PayloadJobResponse"}, "404": {"$ref": "#/components/responses/PayloadJobNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Payload Job", "tags": ["Payload Jobs"], "responses": {"200": {"$ref": "#/components/responses/PayloadJobResponse"}, "404": {"$ref": "#/components/responses/PayloadJobNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-locked-documents": {"get": {"summary": "Retrieve a list of Payload Locked Documents", "tags": ["Payload Locked Documents"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["globalSlug", "-globalSlug", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperations"}, {"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/PayloadLockedDocumentListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Payload Locked Document", "tags": ["Payload Locked Documents"], "requestBody": {"$ref": "#/components/requestBodies/PayloadLockedDocumentRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewPayloadLockedDocumentResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-locked-documents/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Payload Locked Document", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Payload Locked Document by ID", "tags": ["Payload Locked Documents"], "responses": {"200": {"$ref": "#/components/responses/PayloadLockedDocumentResponse"}, "404": {"$ref": "#/components/responses/PayloadLockedDocumentNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Payload Locked Document", "tags": ["Payload Locked Documents"], "responses": {"200": {"$ref": "#/components/responses/PayloadLockedDocumentResponse"}, "404": {"$ref": "#/components/responses/PayloadLockedDocumentNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Payload Locked Document", "tags": ["Payload Locked Documents"], "responses": {"200": {"$ref": "#/components/responses/PayloadLockedDocumentResponse"}, "404": {"$ref": "#/components/responses/PayloadLockedDocumentNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-preferences": {"get": {"summary": "Retrieve a list of Payload Preferences", "tags": ["Payload Preferences"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["key", "-key", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/PayloadPreferenceQueryOperations"}, {"$ref": "#/components/schemas/PayloadPreferenceQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadPreferenceQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/PayloadPreferenceListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Payload Preference", "tags": ["Payload Preferences"], "requestBody": {"$ref": "#/components/requestBodies/PayloadPreferenceRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewPayloadPreferenceResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-preferences/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Payload Preference", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Payload Preference by ID", "tags": ["Payload Preferences"], "responses": {"200": {"$ref": "#/components/responses/PayloadPreferenceResponse"}, "404": {"$ref": "#/components/responses/PayloadPreferenceNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Payload Preference", "tags": ["Payload Preferences"], "responses": {"200": {"$ref": "#/components/responses/PayloadPreferenceResponse"}, "404": {"$ref": "#/components/responses/PayloadPreferenceNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Payload Preference", "tags": ["Payload Preferences"], "responses": {"200": {"$ref": "#/components/responses/PayloadPreferenceResponse"}, "404": {"$ref": "#/components/responses/PayloadPreferenceNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-migrations": {"get": {"summary": "Retrieve a list of Payload Migrations", "tags": ["Payload Migrations"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["name", "-name", "batch", "-batch", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/PayloadMigrationQueryOperations"}, {"$ref": "#/components/schemas/PayloadMigrationQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadMigrationQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/PayloadMigrationListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Payload Migration", "tags": ["Payload Migrations"], "requestBody": {"$ref": "#/components/requestBodies/PayloadMigrationRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewPayloadMigrationResponse"}}, "security": [{"ApiKey": []}]}}, "/api/payload-migrations/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Payload Migration", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Payload Migration by ID", "tags": ["Payload Migrations"], "responses": {"200": {"$ref": "#/components/responses/PayloadMigrationResponse"}, "404": {"$ref": "#/components/responses/PayloadMigrationNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Payload Migration", "tags": ["Payload Migrations"], "responses": {"200": {"$ref": "#/components/responses/PayloadMigrationResponse"}, "404": {"$ref": "#/components/responses/PayloadMigrationNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Payload Migration", "tags": ["Payload Migrations"], "responses": {"200": {"$ref": "#/components/responses/PayloadMigrationResponse"}, "404": {"$ref": "#/components/responses/PayloadMigrationNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/globals/header": {"get": {"summary": "Get the Header", "tags": ["Header"], "parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/HeaderResponse"}}, "security": []}, "post": {"summary": "Update the Header", "tags": ["Header"], "requestBody": {"$ref": "#/components/requestBodies/HeaderRequestBody"}, "responses": {"200": {"$ref": "#/components/responses/HeaderResponse"}}, "security": [{"ApiKey": []}]}}, "/api/globals/footer": {"get": {"summary": "Get the Footer", "tags": ["Footer"], "parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/FooterResponse"}}, "security": []}, "post": {"summary": "Update the Footer", "tags": ["Footer"], "requestBody": {"$ref": "#/components/requestBodies/FooterRequestBody"}, "responses": {"200": {"$ref": "#/components/responses/FooterResponse"}}, "security": [{"ApiKey": []}]}}}, "components": {"securitySchemes": {"ApiKey": {"type": "oauth2", "flows": {"password": {"tokenUrl": "/api//openapi-auth", "scopes": {}}}}}, "schemas": {"supportedTimezones": {"type": "string", "example": "Europe/Prague"}, "Page": {"type": "object", "additionalProperties": false, "title": "Page", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "hero": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["none", "highImpact", "mediumImpact", "lowImpact"]}, "richText": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "links": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}, "appearance": {"description": "Choose how the link should be rendered.", "type": "string", "enum": ["default", "outline"], "nullable": true}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "media": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}}, "required": ["type"]}, "layout": {"type": "array", "items": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"richText": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "links": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}, "appearance": {"description": "Choose how the link should be rendered.", "type": "string", "enum": ["default", "outline"], "nullable": true}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["cta"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"columns": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"size": {"type": "string", "enum": ["oneThird", "half", "twoThirds", "full"], "nullable": true}, "richText": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "enableLink": {"type": "boolean", "nullable": true}, "link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}, "appearance": {"description": "Choose how the link should be rendered.", "type": "string", "enum": ["default", "outline"], "nullable": true}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["content"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"media": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Media"}]}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["mediaBlock"]}}, "required": ["blockType", "media"]}, {"type": "object", "additionalProperties": false, "properties": {"introContent": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "populateBy": {"type": "string", "enum": ["collection", "selection"], "nullable": true}, "relationTo": {"type": "string", "enum": ["posts"], "nullable": true}, "categories": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Category"}]}, "nullable": true}, "limit": {"type": "number", "nullable": true}, "selectedDocs": {"type": "array", "items": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"]}]}, "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["archive"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"form": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Form"}]}, "enableIntro": {"type": "boolean", "nullable": true}, "introContent": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["formBlock"]}}, "required": ["blockType", "form"]}]}}, "meta": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "image": {"description": "Maximum upload file size: 12MB. Recommended file size for images is <500KB.", "oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "description": {"type": "string", "nullable": true}}, "required": []}, "publishedAt": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "slugLock": {"type": "boolean", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "_status": {"type": "string", "enum": ["draft", "published"], "nullable": true}}, "required": ["id", "title", "hero", "layout", "updatedAt", "createdAt"]}, "Post": {"type": "object", "additionalProperties": false, "title": "Post", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "heroImage": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "content": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"]}, "relatedPosts": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}, "nullable": true}, "categories": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Category"}]}, "nullable": true}, "meta": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "image": {"description": "Maximum upload file size: 12MB. Recommended file size for images is <500KB.", "oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "description": {"type": "string", "nullable": true}}, "required": []}, "publishedAt": {"type": "string", "nullable": true}, "authors": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/User"}]}, "nullable": true}, "populatedAuthors": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}, "slug": {"type": "string", "nullable": true}, "slugLock": {"type": "boolean", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "_status": {"type": "string", "enum": ["draft", "published"], "nullable": true}}, "required": ["id", "title", "content", "updatedAt", "createdAt"]}, "Media": {"type": "object", "additionalProperties": false, "title": "Media", "properties": {"id": {"type": "string"}, "alt": {"type": "string", "nullable": true}, "caption": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "prefix": {"type": "string", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "url": {"type": "string", "nullable": true}, "thumbnailURL": {"type": "string", "nullable": true}, "filename": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "focalX": {"type": "number", "nullable": true}, "focalY": {"type": "number", "nullable": true}, "sizes": {"type": "object", "additionalProperties": false, "properties": {"thumbnail": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "square": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "small": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "medium": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "large": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "xlarge": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "og": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}}, "required": []}}, "required": ["id", "updatedAt", "createdAt"]}, "Category": {"type": "object", "additionalProperties": false, "title": "Category", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "slug": {"type": "string", "nullable": true}, "slugLock": {"type": "boolean", "nullable": true}, "parent": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Category"}]}, "breadcrumbs": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"doc": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Category"}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "title", "updatedAt", "createdAt"]}, "User": {"type": "object", "additionalProperties": false, "title": "User", "properties": {"id": {"type": "string"}, "name": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string", "enum": ["subscriber", "editor", "admin", "super-admin"]}}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "email": {"type": "string"}, "resetPasswordToken": {"type": "string", "nullable": true}, "resetPasswordExpiration": {"type": "string", "nullable": true}, "salt": {"type": "string", "nullable": true}, "hash": {"type": "string", "nullable": true}, "loginAttempts": {"type": "number", "nullable": true}, "lockUntil": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "required": ["id", "roles", "updatedAt", "createdAt", "email"]}, "Redirect": {"type": "object", "additionalProperties": false, "title": "Redirect", "properties": {"id": {"type": "string"}, "from": {"description": "You will need to rebuild the website when changing this field.", "type": "string"}, "to": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}}, "required": []}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "from", "updatedAt", "createdAt"]}, "Form": {"type": "object", "additionalProperties": false, "title": "Form", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "fields": {"type": "array", "items": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "defaultValue": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["checkbox"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["country"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["email"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"message": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["message"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["number"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "options": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"label": {"type": "string"}, "value": {"type": "string"}, "id": {"type": "string", "nullable": true}}, "required": ["label", "value"]}, "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["select"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["state"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["text"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["textarea"]}}, "required": ["blockType", "name"]}]}, "nullable": true}, "submitButtonLabel": {"type": "string", "nullable": true}, "confirmationType": {"description": "Choose whether to display an on-page message or redirect to a different page after they submit the form.", "type": "string", "enum": ["message", "redirect"], "nullable": true}, "confirmationMessage": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "redirect": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string"}}, "required": ["url"]}, "emails": {"description": "Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"emailTo": {"type": "string", "nullable": true}, "cc": {"type": "string", "nullable": true}, "bcc": {"type": "string", "nullable": true}, "replyTo": {"type": "string", "nullable": true}, "emailFrom": {"type": "string", "nullable": true}, "subject": {"type": "string"}, "message": {"description": "Enter the message that should be sent in this email.", "type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": ["subject"]}, "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "title", "updatedAt", "createdAt"]}, "FormSubmission": {"type": "object", "additionalProperties": false, "title": "Form Submission", "properties": {"id": {"type": "string"}, "form": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Form"}]}, "submissionData": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"field": {"type": "string"}, "value": {"type": "string"}, "id": {"type": "string", "nullable": true}}, "required": ["field", "value"]}, "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "form", "updatedAt", "createdAt"]}, "SearchResult": {"type": "object", "additionalProperties": false, "title": "Search Result", "properties": {"id": {"type": "string"}, "title": {"type": "string", "nullable": true}, "priority": {"type": "number", "nullable": true}, "doc": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"]}]}, "slug": {"type": "string", "nullable": true}, "meta": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "image": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}}, "required": []}, "categories": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"type": "string", "nullable": true}, "categoryID": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "doc", "updatedAt", "createdAt"], "description": "This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated."}, "PayloadJob": {"type": "object", "additionalProperties": false, "title": "Payload Job", "properties": {"id": {"type": "string"}, "input": {"description": "Input data provided to the job", "nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "taskStatus": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "completedAt": {"type": "string", "nullable": true}, "totalTried": {"type": "number", "nullable": true}, "hasError": {"description": "If has<PERSON>rror is true this job will not be retried", "type": "boolean", "nullable": true}, "error": {"description": "If hasError is true, this is the error that caused it", "nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "log": {"description": "Task execution log", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"executedAt": {"type": "string"}, "completedAt": {"type": "string"}, "taskSlug": {"type": "string", "enum": ["inline", "schedulePublish"]}, "taskID": {"type": "string"}, "input": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "output": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "state": {"type": "string", "enum": ["failed", "succeeded"]}, "error": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "id": {"type": "string", "nullable": true}}, "required": ["executedAt", "completedAt", "taskSlug", "taskID", "state"]}, "nullable": true}, "taskSlug": {"type": "string", "enum": ["inline", "schedulePublish"], "nullable": true}, "queue": {"type": "string", "nullable": true}, "waitUntil": {"type": "string", "nullable": true}, "processing": {"type": "boolean", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "updatedAt", "createdAt"]}, "PayloadLockedDocument": {"type": "object", "additionalProperties": false, "title": "Payload Locked Document", "properties": {"id": {"type": "string"}, "document": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["media"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Media"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["categories"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Category"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["users"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/User"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["redirects"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Redirect"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["forms"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Form"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["form-submissions"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/FormSubmission"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["search"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/SearchResult"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["payload-jobs"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/PayloadJob"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "globalSlug": {"type": "string", "nullable": true}, "user": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["users"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/User"}]}}, "required": ["value", "relationTo"]}]}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "user", "updatedAt", "createdAt"]}, "PayloadPreference": {"type": "object", "additionalProperties": false, "title": "Payload Preference", "properties": {"id": {"type": "string"}, "user": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["users"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/User"}]}}, "required": ["value", "relationTo"]}]}, "key": {"type": "string", "nullable": true}, "value": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "user", "updatedAt", "createdAt"]}, "PayloadMigration": {"type": "object", "additionalProperties": false, "title": "Payload Migration", "properties": {"id": {"type": "string"}, "name": {"type": "string", "nullable": true}, "batch": {"type": "number", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "updatedAt", "createdAt"]}, "PageQueryOperations": {"title": "Page query operations", "type": "object", "properties": {"title": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "publishedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "slug": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "slugLock": {"type": "object", "properties": {"equals": {"type": "boolean"}, "not_equals": {"type": "boolean"}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "_status": {"type": "object", "properties": {"equals": {"type": "string", "enum": ["draft", "published"]}, "not_equals": {"type": "string", "enum": ["draft", "published"]}, "in": {"type": "string"}, "not_in": {"type": "string"}}}}}, "PageQueryOperationsAnd": {"title": "Page query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PageQueryOperations"}, {"$ref": "#/components/schemas/PageQueryOperationsAnd"}, {"$ref": "#/components/schemas/PageQueryOperationsOr"}]}}}, "required": ["and"]}, "PageQueryOperationsOr": {"title": "Page query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PageQueryOperations"}, {"$ref": "#/components/schemas/PageQueryOperationsAnd"}, {"$ref": "#/components/schemas/PageQueryOperationsOr"}]}}}, "required": ["or"]}, "PostQueryOperations": {"title": "Post query operations", "type": "object", "properties": {"title": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "publishedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "slug": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "slugLock": {"type": "object", "properties": {"equals": {"type": "boolean"}, "not_equals": {"type": "boolean"}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "_status": {"type": "object", "properties": {"equals": {"type": "string", "enum": ["draft", "published"]}, "not_equals": {"type": "string", "enum": ["draft", "published"]}, "in": {"type": "string"}, "not_in": {"type": "string"}}}}}, "PostQueryOperationsAnd": {"title": "Post query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PostQueryOperations"}, {"$ref": "#/components/schemas/PostQueryOperationsAnd"}, {"$ref": "#/components/schemas/PostQueryOperationsOr"}]}}}, "required": ["and"]}, "PostQueryOperationsOr": {"title": "Post query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PostQueryOperations"}, {"$ref": "#/components/schemas/PostQueryOperationsAnd"}, {"$ref": "#/components/schemas/PostQueryOperationsOr"}]}}}, "required": ["or"]}, "MediaQueryOperations": {"title": "Media query operations", "type": "object", "properties": {"alt": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "prefix": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "url": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "thumbnailURL": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "filename": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "mimeType": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "filesize": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "width": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "height": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "focalX": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "focalY": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}}}, "MediaQueryOperationsAnd": {"title": "Media query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/MediaQueryOperations"}, {"$ref": "#/components/schemas/MediaQueryOperationsAnd"}, {"$ref": "#/components/schemas/MediaQueryOperationsOr"}]}}}, "required": ["and"]}, "MediaQueryOperationsOr": {"title": "Media query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/MediaQueryOperations"}, {"$ref": "#/components/schemas/MediaQueryOperationsAnd"}, {"$ref": "#/components/schemas/MediaQueryOperationsOr"}]}}}, "required": ["or"]}, "CategoryQueryOperations": {"title": "Category query operations", "type": "object", "properties": {"title": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "slug": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "slugLock": {"type": "object", "properties": {"equals": {"type": "boolean"}, "not_equals": {"type": "boolean"}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "CategoryQueryOperationsAnd": {"title": "Category query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/CategoryQueryOperations"}, {"$ref": "#/components/schemas/CategoryQueryOperationsAnd"}, {"$ref": "#/components/schemas/CategoryQueryOperationsOr"}]}}}, "required": ["and"]}, "CategoryQueryOperationsOr": {"title": "Category query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/CategoryQueryOperations"}, {"$ref": "#/components/schemas/CategoryQueryOperationsAnd"}, {"$ref": "#/components/schemas/CategoryQueryOperationsOr"}]}}}, "required": ["or"]}, "UserQueryOperations": {"title": "User query operations", "type": "object", "properties": {"name": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "roles": {"type": "object", "properties": {"equals": {"type": "string", "enum": ["subscriber", "editor", "admin", "super-admin"]}, "not_equals": {"type": "string", "enum": ["subscriber", "editor", "admin", "super-admin"]}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "email": {"type": "object", "properties": {"equals": {"type": "string", "format": "email"}, "not_equals": {"type": "string", "format": "email"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "contains": {"type": "string", "format": "email"}}}, "resetPasswordToken": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "resetPasswordExpiration": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "salt": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "hash": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "loginAttempts": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "lockUntil": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "UserQueryOperationsAnd": {"title": "User query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/UserQueryOperations"}, {"$ref": "#/components/schemas/UserQueryOperationsAnd"}, {"$ref": "#/components/schemas/UserQueryOperationsOr"}]}}}, "required": ["and"]}, "UserQueryOperationsOr": {"title": "User query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/UserQueryOperations"}, {"$ref": "#/components/schemas/UserQueryOperationsAnd"}, {"$ref": "#/components/schemas/UserQueryOperationsOr"}]}}}, "required": ["or"]}, "RedirectQueryOperations": {"title": "Redirect query operations", "type": "object", "properties": {"from": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "RedirectQueryOperationsAnd": {"title": "Redirect query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/RedirectQueryOperations"}, {"$ref": "#/components/schemas/RedirectQueryOperationsAnd"}, {"$ref": "#/components/schemas/RedirectQueryOperationsOr"}]}}}, "required": ["and"]}, "RedirectQueryOperationsOr": {"title": "Redirect query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/RedirectQueryOperations"}, {"$ref": "#/components/schemas/RedirectQueryOperationsAnd"}, {"$ref": "#/components/schemas/RedirectQueryOperationsOr"}]}}}, "required": ["or"]}, "FormQueryOperations": {"title": "Form query operations", "type": "object", "properties": {"title": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "submitButtonLabel": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "confirmationType": {"type": "object", "properties": {"equals": {"type": "string", "enum": ["message", "redirect"]}, "not_equals": {"type": "string", "enum": ["message", "redirect"]}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "FormQueryOperationsAnd": {"title": "Form query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/FormQueryOperations"}, {"$ref": "#/components/schemas/FormQueryOperationsAnd"}, {"$ref": "#/components/schemas/FormQueryOperationsOr"}]}}}, "required": ["and"]}, "FormQueryOperationsOr": {"title": "Form query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/FormQueryOperations"}, {"$ref": "#/components/schemas/FormQueryOperationsAnd"}, {"$ref": "#/components/schemas/FormQueryOperationsOr"}]}}}, "required": ["or"]}, "FormSubmissionQueryOperations": {"title": "Form Submission query operations", "type": "object", "properties": {"updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "FormSubmissionQueryOperationsAnd": {"title": "Form Submission query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/FormSubmissionQueryOperations"}, {"$ref": "#/components/schemas/FormSubmissionQueryOperationsAnd"}, {"$ref": "#/components/schemas/FormSubmissionQueryOperationsOr"}]}}}, "required": ["and"]}, "FormSubmissionQueryOperationsOr": {"title": "Form Submission query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/FormSubmissionQueryOperations"}, {"$ref": "#/components/schemas/FormSubmissionQueryOperationsAnd"}, {"$ref": "#/components/schemas/FormSubmissionQueryOperationsOr"}]}}}, "required": ["or"]}, "SearchResultQueryOperations": {"title": "Search Result query operations", "type": "object", "properties": {"title": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "priority": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "slug": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "SearchResultQueryOperationsAnd": {"title": "Search Result query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/SearchResultQueryOperations"}, {"$ref": "#/components/schemas/SearchResultQueryOperationsAnd"}, {"$ref": "#/components/schemas/SearchResultQueryOperationsOr"}]}}}, "required": ["and"]}, "SearchResultQueryOperationsOr": {"title": "Search Result query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/SearchResultQueryOperations"}, {"$ref": "#/components/schemas/SearchResultQueryOperationsAnd"}, {"$ref": "#/components/schemas/SearchResultQueryOperationsOr"}]}}}, "required": ["or"]}, "PayloadJobQueryOperations": {"title": "Payload Job query operations", "type": "object", "properties": {"taskSlug": {"type": "object", "properties": {"equals": {"type": "string", "enum": ["inline", "schedulePublish"]}, "not_equals": {"type": "string", "enum": ["inline", "schedulePublish"]}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "queue": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "waitUntil": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "processing": {"type": "object", "properties": {"equals": {"type": "boolean"}, "not_equals": {"type": "boolean"}, "in": {"type": "string"}, "not_in": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "PayloadJobQueryOperationsAnd": {"title": "Payload Job query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadJobQueryOperations"}, {"$ref": "#/components/schemas/PayloadJobQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadJobQueryOperationsOr"}]}}}, "required": ["and"]}, "PayloadJobQueryOperationsOr": {"title": "Payload Job query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadJobQueryOperations"}, {"$ref": "#/components/schemas/PayloadJobQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadJobQueryOperationsOr"}]}}}, "required": ["or"]}, "PayloadLockedDocumentQueryOperations": {"title": "Payload Locked Document query operations", "type": "object", "properties": {"globalSlug": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "PayloadLockedDocumentQueryOperationsAnd": {"title": "Payload Locked Document query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperations"}, {"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperationsOr"}]}}}, "required": ["and"]}, "PayloadLockedDocumentQueryOperationsOr": {"title": "Payload Locked Document query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperations"}, {"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadLockedDocumentQueryOperationsOr"}]}}}, "required": ["or"]}, "PayloadPreferenceQueryOperations": {"title": "Payload Preference query operations", "type": "object", "properties": {"key": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "PayloadPreferenceQueryOperationsAnd": {"title": "Payload Preference query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadPreferenceQueryOperations"}, {"$ref": "#/components/schemas/PayloadPreferenceQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadPreferenceQueryOperationsOr"}]}}}, "required": ["and"]}, "PayloadPreferenceQueryOperationsOr": {"title": "Payload Preference query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadPreferenceQueryOperations"}, {"$ref": "#/components/schemas/PayloadPreferenceQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadPreferenceQueryOperationsOr"}]}}}, "required": ["or"]}, "PayloadMigrationQueryOperations": {"title": "Payload Migration query operations", "type": "object", "properties": {"name": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "batch": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "PayloadMigrationQueryOperationsAnd": {"title": "Payload Migration query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadMigrationQueryOperations"}, {"$ref": "#/components/schemas/PayloadMigrationQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadMigrationQueryOperationsOr"}]}}}, "required": ["and"]}, "PayloadMigrationQueryOperationsOr": {"title": "Payload Migration query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/PayloadMigrationQueryOperations"}, {"$ref": "#/components/schemas/PayloadMigrationQueryOperationsAnd"}, {"$ref": "#/components/schemas/PayloadMigrationQueryOperationsOr"}]}}}, "required": ["or"]}, "Header": {"type": "object", "additionalProperties": false, "title": "Header", "properties": {"id": {"type": "string"}, "navItems": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "updatedAt": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "nullable": true}}, "required": ["id"]}, "HeaderRead": {"title": "Header (if present)", "oneOf": [{"type": "object", "additionalProperties": false, "title": "Header", "properties": {"id": {"type": "string"}, "navItems": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "updatedAt": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "nullable": true}}, "required": ["id"]}, {"type": "object", "properties": {}}]}, "HeaderWrite": {"type": "object", "additionalProperties": false, "title": "Header (writable fields)", "properties": {"navItems": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}}, "required": ["id"]}, "Footer": {"type": "object", "additionalProperties": false, "title": "Footer", "properties": {"id": {"type": "string"}, "navItems": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "updatedAt": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "nullable": true}}, "required": ["id"]}, "FooterRead": {"title": "Footer (if present)", "oneOf": [{"type": "object", "additionalProperties": false, "title": "Footer", "properties": {"id": {"type": "string"}, "navItems": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "updatedAt": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "nullable": true}}, "required": ["id"]}, {"type": "object", "properties": {}}]}, "FooterWrite": {"type": "object", "additionalProperties": false, "title": "Footer (writable fields)", "properties": {"navItems": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}}, "required": ["id"]}}, "requestBodies": {"PageRequestBody": {"description": "Page", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Page", "properties": {"title": {"type": "string"}, "hero": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["none", "highImpact", "mediumImpact", "lowImpact"]}, "richText": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "links": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}, "appearance": {"description": "Choose how the link should be rendered.", "type": "string", "enum": ["default", "outline"], "nullable": true}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "media": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}}, "required": ["type"]}, "layout": {"type": "array", "items": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"richText": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "links": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}, "appearance": {"description": "Choose how the link should be rendered.", "type": "string", "enum": ["default", "outline"], "nullable": true}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": ["link"]}, "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["cta"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"columns": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"size": {"type": "string", "enum": ["oneThird", "half", "twoThirds", "full"], "nullable": true}, "richText": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "enableLink": {"type": "boolean", "nullable": true}, "link": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "newTab": {"type": "boolean", "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string"}, "appearance": {"description": "Choose how the link should be rendered.", "type": "string", "enum": ["default", "outline"], "nullable": true}}, "required": ["label"]}, "id": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["content"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"media": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Media"}]}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["mediaBlock"]}}, "required": ["blockType", "media"]}, {"type": "object", "additionalProperties": false, "properties": {"introContent": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "populateBy": {"type": "string", "enum": ["collection", "selection"], "nullable": true}, "relationTo": {"type": "string", "enum": ["posts"], "nullable": true}, "categories": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Category"}]}, "nullable": true}, "limit": {"type": "number", "nullable": true}, "selectedDocs": {"type": "array", "items": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"]}]}, "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["archive"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"form": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Form"}]}, "enableIntro": {"type": "boolean", "nullable": true}, "introContent": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["formBlock"]}}, "required": ["blockType", "form"]}]}}, "meta": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "image": {"description": "Maximum upload file size: 12MB. Recommended file size for images is <500KB.", "oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "description": {"type": "string", "nullable": true}}, "required": []}, "publishedAt": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "slugLock": {"type": "boolean", "nullable": true}, "_status": {"type": "string", "enum": ["draft", "published"], "nullable": true}}, "required": ["id", "title", "hero", "layout", "updatedAt", "createdAt"]}}}}, "PostRequestBody": {"description": "Post", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Post", "properties": {"title": {"type": "string"}, "heroImage": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "content": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"]}, "relatedPosts": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}, "nullable": true}, "categories": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Category"}]}, "nullable": true}, "meta": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "image": {"description": "Maximum upload file size: 12MB. Recommended file size for images is <500KB.", "oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "description": {"type": "string", "nullable": true}}, "required": []}, "publishedAt": {"type": "string", "nullable": true}, "authors": {"type": "string", "description": "ID of the users"}, "populatedAuthors": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}, "slug": {"type": "string", "nullable": true}, "slugLock": {"type": "boolean", "nullable": true}, "_status": {"type": "string", "enum": ["draft", "published"], "nullable": true}}, "required": ["id", "title", "content", "updatedAt", "createdAt"]}}}}, "MediaRequestBody": {"description": "Media", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Media", "properties": {"alt": {"type": "string", "nullable": true}, "caption": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "prefix": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "thumbnailURL": {"type": "string", "nullable": true}, "filename": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "focalX": {"type": "number", "nullable": true}, "focalY": {"type": "number", "nullable": true}, "sizes": {"type": "object", "additionalProperties": false, "properties": {"thumbnail": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "square": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "small": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "medium": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "large": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "xlarge": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "og": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}}, "required": []}}, "required": ["id", "updatedAt", "createdAt"]}}}}, "CategoryRequestBody": {"description": "Category", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Category", "properties": {"title": {"type": "string"}, "slug": {"type": "string", "nullable": true}, "slugLock": {"type": "boolean", "nullable": true}, "parent": {"type": "string", "description": "ID of the categories"}, "breadcrumbs": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"doc": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Category"}]}, "url": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}}, "required": ["id", "title", "updatedAt", "createdAt"]}}}}, "UserRequestBody": {"description": "User", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "User", "properties": {"name": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string", "enum": ["subscriber", "editor", "admin", "super-admin"]}}, "email": {"type": "string"}, "resetPasswordToken": {"type": "string", "nullable": true}, "resetPasswordExpiration": {"type": "string", "nullable": true}, "salt": {"type": "string", "nullable": true}, "hash": {"type": "string", "nullable": true}, "loginAttempts": {"type": "number", "nullable": true}, "lockUntil": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "required": ["id", "roles", "updatedAt", "createdAt", "email"]}}}}, "RedirectRequestBody": {"description": "Redirect", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Redirect", "properties": {"from": {"description": "You will need to rebuild the website when changing this field.", "type": "string"}, "to": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["reference", "custom"], "nullable": true}, "reference": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["pages"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Page"}]}}, "required": ["value", "relationTo"], "nullable": true}, {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"enum": ["posts"]}, "value": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Post"}]}}, "required": ["value", "relationTo"], "nullable": true}]}, "url": {"type": "string", "nullable": true}}, "required": []}}, "required": ["id", "from", "updatedAt", "createdAt"]}}}}, "FormRequestBody": {"description": "Form", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Form", "properties": {"title": {"type": "string"}, "fields": {"type": "array", "items": {"oneOf": [{"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "defaultValue": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["checkbox"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["country"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["email"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"message": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["message"]}}, "required": ["blockType"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["number"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "options": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"label": {"type": "string"}, "value": {"type": "string"}, "id": {"type": "string", "nullable": true}}, "required": ["label", "value"]}, "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["select"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["state"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["text"]}}, "required": ["blockType", "name"]}, {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "defaultValue": {"type": "string", "nullable": true}, "required": {"type": "boolean", "nullable": true}, "id": {"type": "string", "nullable": true}, "blockName": {"type": "string", "nullable": true}, "blockType": {"enum": ["textarea"]}}, "required": ["blockType", "name"]}]}, "nullable": true}, "submitButtonLabel": {"type": "string", "nullable": true}, "confirmationType": {"description": "Choose whether to display an on-page message or redirect to a different page after they submit the form.", "type": "string", "enum": ["message", "redirect"], "nullable": true}, "confirmationMessage": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "redirect": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string"}}, "required": ["url"]}, "emails": {"description": "Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"emailTo": {"type": "string", "nullable": true}, "cc": {"type": "string", "nullable": true}, "bcc": {"type": "string", "nullable": true}, "replyTo": {"type": "string", "nullable": true}, "emailFrom": {"type": "string", "nullable": true}, "subject": {"type": "string"}, "message": {"description": "Enter the message that should be sent in this email.", "type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": ["subject"]}, "nullable": true}}, "required": ["id", "title", "updatedAt", "createdAt"]}}}}, "FormSubmissionRequestBody": {"description": "Form Submission", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "FormSubmission", "properties": {"form": {"type": "string", "description": "ID of the forms"}, "submissionData": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"field": {"type": "string"}, "value": {"type": "string"}, "id": {"type": "string", "nullable": true}}, "required": ["field", "value"]}, "nullable": true}}, "required": ["id", "form", "updatedAt", "createdAt"]}}}}, "SearchResultRequestBody": {"description": "Search Result", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Search", "properties": {"title": {"type": "string", "nullable": true}, "priority": {"type": "number", "nullable": true}, "doc": {"type": "string", "description": "ID of the posts"}, "slug": {"type": "string", "nullable": true}, "meta": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "image": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}}, "required": []}, "categories": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"relationTo": {"type": "string", "nullable": true}, "categoryID": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": []}, "nullable": true}}, "required": ["id", "doc", "updatedAt", "createdAt"], "description": "This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated."}}}}, "PayloadJobRequestBody": {"description": "Payload Job", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "PayloadJob", "properties": {"input": {"description": "Input data provided to the job", "nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "taskStatus": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "completedAt": {"type": "string", "nullable": true}, "totalTried": {"type": "number", "nullable": true}, "hasError": {"description": "If has<PERSON>rror is true this job will not be retried", "type": "boolean", "nullable": true}, "error": {"description": "If hasError is true, this is the error that caused it", "nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "log": {"description": "Task execution log", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"executedAt": {"type": "string"}, "completedAt": {"type": "string"}, "taskSlug": {"type": "string", "enum": ["inline", "schedulePublish"]}, "taskID": {"type": "string"}, "input": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "output": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "state": {"type": "string", "enum": ["failed", "succeeded"]}, "error": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "id": {"type": "string", "nullable": true}}, "required": ["executedAt", "completedAt", "taskSlug", "taskID", "state"]}, "nullable": true}, "taskSlug": {"type": "string", "enum": ["inline", "schedulePublish"], "nullable": true}, "queue": {"type": "string", "nullable": true}, "waitUntil": {"type": "string", "nullable": true}, "processing": {"type": "boolean", "nullable": true}}, "required": ["id", "updatedAt", "createdAt"]}}}}, "PayloadLockedDocumentRequestBody": {"description": "Payload Locked Document", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "PayloadLockedDocument", "properties": {"document": {"type": "string", "description": "ID of the pages/posts/media/categories/users/redirects/forms/form-submissions/search/payload-jobs"}, "globalSlug": {"type": "string", "nullable": true}, "user": {"type": "string", "description": "ID of the users"}}, "required": ["id", "user", "updatedAt", "createdAt"]}}}}, "PayloadPreferenceRequestBody": {"description": "Payload Preference", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "PayloadPreference", "properties": {"user": {"type": "string", "description": "ID of the users"}, "key": {"type": "string", "nullable": true}, "value": {"nullable": true, "anyOf": [{"type": "object"}, {"type": "array", "items": {}}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}, "required": ["id", "user", "updatedAt", "createdAt"]}}}}, "PayloadMigrationRequestBody": {"description": "Payload Migration", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "PayloadMigration", "properties": {"name": {"type": "string", "nullable": true}, "batch": {"type": "number", "nullable": true}}, "required": ["id", "updatedAt", "createdAt"]}}}}, "HeaderRequestBody": {"description": "Header", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderWrite"}}}}, "FooterRequestBody": {"description": "Footer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FooterWrite"}}}}}, "responses": {"PageResponse": {"description": "Page object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}, "NewPageResponse": {"description": "Page object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Page"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "PageNotFoundResponse": {"description": "Page not found", "content": {}}, "PageListResponse": {"description": "List of Pages", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Page"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "PostResponse": {"description": "Post object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}, "NewPostResponse": {"description": "Post object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Post"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "PostNotFoundResponse": {"description": "Post not found", "content": {}}, "PostListResponse": {"description": "List of Posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "MediaResponse": {"description": "Media object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Media"}}}}, "NewMediaResponse": {"description": "Media object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Media"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "MediaNotFoundResponse": {"description": "Media not found", "content": {}}, "MediaListResponse": {"description": "List of Media", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Media"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "CategoryResponse": {"description": "Category object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}, "NewCategoryResponse": {"description": "Category object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Category"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "CategoryNotFoundResponse": {"description": "Category not found", "content": {}}, "CategoryListResponse": {"description": "List of Categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "UserResponse": {"description": "User object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "NewUserResponse": {"description": "User object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/User"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "UserNotFoundResponse": {"description": "User not found", "content": {}}, "UserListResponse": {"description": "List of Users", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "RedirectResponse": {"description": "Redirect object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Redirect"}}}}, "NewRedirectResponse": {"description": "Redirect object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Redirect"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "RedirectNotFoundResponse": {"description": "Redirect not found", "content": {}}, "RedirectListResponse": {"description": "List of Redirects", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Redirect"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "FormResponse": {"description": "Form object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}}}}, "NewFormResponse": {"description": "Form object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Form"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "FormNotFoundResponse": {"description": "Form not found", "content": {}}, "FormListResponse": {"description": "List of Forms", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Form"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "FormSubmissionResponse": {"description": "Form Submission object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormSubmission"}}}}, "NewFormSubmissionResponse": {"description": "Form Submission object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/FormSubmission"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "FormSubmissionNotFoundResponse": {"description": "Form Submission not found", "content": {}}, "FormSubmissionListResponse": {"description": "List of Form Submissions", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/FormSubmission"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "SearchResultResponse": {"description": "Search Result object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResult"}}}}, "NewSearchResultResponse": {"description": "Search Result object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/SearchResult"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "SearchResultNotFoundResponse": {"description": "Search Result not found", "content": {}}, "SearchResultListResponse": {"description": "List of Search Results", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResult"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "PayloadJobResponse": {"description": "Payload Job object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayloadJob"}}}}, "NewPayloadJobResponse": {"description": "Payload Job object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/PayloadJob"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "PayloadJobNotFoundResponse": {"description": "Payload Job not found", "content": {}}, "PayloadJobListResponse": {"description": "List of Payload Jobs", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/PayloadJob"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "PayloadLockedDocumentResponse": {"description": "Payload Locked Document object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayloadLockedDocument"}}}}, "NewPayloadLockedDocumentResponse": {"description": "Payload Locked Document object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/PayloadLockedDocument"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "PayloadLockedDocumentNotFoundResponse": {"description": "Payload Locked Document not found", "content": {}}, "PayloadLockedDocumentListResponse": {"description": "List of Payload Locked Documents", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/PayloadLockedDocument"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "PayloadPreferenceResponse": {"description": "Payload Preference object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayloadPreference"}}}}, "NewPayloadPreferenceResponse": {"description": "Payload Preference object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/PayloadPreference"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "PayloadPreferenceNotFoundResponse": {"description": "Payload Preference not found", "content": {}}, "PayloadPreferenceListResponse": {"description": "List of Payload Preferences", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/PayloadPreference"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "PayloadMigrationResponse": {"description": "Payload Migration object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayloadMigration"}}}}, "NewPayloadMigrationResponse": {"description": "Payload Migration object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/PayloadMigration"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "PayloadMigrationNotFoundResponse": {"description": "Payload Migration not found", "content": {}}, "PayloadMigrationListResponse": {"description": "List of Payload Migrations", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/PayloadMigration"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "HeaderResponse": {"description": "Header", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderRead"}}}}, "FooterResponse": {"description": "Footer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FooterRead"}}}}}}}