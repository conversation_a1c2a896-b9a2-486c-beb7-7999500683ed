{"openapi": "3.0.0", "info": {"title": "SERPlens API", "version": "1.0.0", "description": "Auto-generated OpenAPI document for the SERPlens API."}, "paths": {"/api/users": {"get": {"summary": "List all users", "operationId": "findUsers", "tags": ["users"], "responses": {"200": {"description": "A list of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new user", "operationId": "createUser", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created user", "content": {"application/json": {"schema": {"type": "object"}}}}}}, "patch": {"summary": "Update users", "operationId": "updateUsers", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated users", "content": {"application/json": {"schema": {"type": "object"}}}}}}, "delete": {"summary": "Delete users", "operationId": "deleteUsers", "tags": ["users"], "responses": {"204": {"description": "users deleted successfully"}}}}, "/api/users/{id}": {"get": {"summary": "Retrieve a user by ID", "operationId": "findUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single user", "content": {"application/json": {"schema": {"type": "object"}}}}}}, "patch": {"summary": "Update a user by ID", "operationId": "updateUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated user", "content": {"application/json": {"schema": {"type": "object"}}}}}}, "delete": {"summary": "Delete a user by ID", "operationId": "deleteUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "user deleted"}}}}, "/api/users/count": {"get": {"summary": "Count of users", "operationId": "countUsers", "tags": ["users"], "responses": {"200": {"description": "Count of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/users/login": {"post": {"summary": "<PERSON><PERSON>", "operationId": "login", "tags": ["auth"], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/logout": {"post": {"summary": "Logout", "operationId": "logout", "tags": ["auth"], "responses": {"200": {"description": "Logout", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/unlock": {"post": {"summary": "Unlock", "operationId": "unlock", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Unlock", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/refresh-token": {"post": {"summary": "Refresh token", "operationId": "refreshToken", "tags": ["auth"], "responses": {"200": {"description": "Refresh token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/me": {"get": {"summary": "Current user", "operationId": "currentUser", "tags": ["auth"], "responses": {"200": {"description": "Current user", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/forgot-password": {"post": {"summary": "Forgot password", "operationId": "forgotPassword", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Forgot password", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/reset-password": {"post": {"summary": "Reset password", "operationId": "resetPassword", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "password": {"type": "string"}}}}}}, "responses": {"200": {"description": "Reset password", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/verify/{token}": {"post": {"summary": "Verify token", "operationId": "verifyToken", "tags": ["auth"], "responses": {"200": {"description": "Verify token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}}}