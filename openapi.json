{"openapi": "3.0.0", "info": {"title": "SERPlens API", "version": "1.0.0", "description": "Auto-generated OpenAPI document for the SERPlens API."}, "paths": {"/api/pages": {"get": {"summary": "List all pages", "operationId": "findPages", "tags": ["pages"], "parameters": [{"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100}, "description": "Limit number of documents returned"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1}, "description": "Page number for pagination"}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Field(s) to sort by"}, {"name": "where", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Advanced filtering (JSON string)"}], "responses": {"200": {"description": "A list of pages", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Page"}}, "totalDocs": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1}, "totalPages": {"type": "integer", "minimum": 0}, "page": {"type": "integer", "minimum": 1}, "pagingCounter": {"type": "integer", "minimum": 1}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "minimum": 1, "nullable": true}, "nextPage": {"type": "integer", "minimum": 1, "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage"]}}}}}}, "post": {"summary": "Create a new page", "operationId": "createPage", "tags": ["pages"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}, "responses": {"201": {"description": "Created page", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}}}, "patch": {"summary": "Update pages", "operationId": "updatePages", "tags": ["pages"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}, "responses": {"200": {"description": "Updated pages", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}}}, "delete": {"summary": "Delete pages", "operationId": "deletePages", "tags": ["pages"], "responses": {"200": {"description": "pages deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "deletedCount": {"type": "integer"}}, "required": ["message"]}}}}}}}, "/api/pages/{id}": {"get": {"summary": "Retrieve a page by ID", "operationId": "findPageById", "tags": ["pages"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}], "responses": {"200": {"description": "A single page", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}}}, "patch": {"summary": "Update a page by ID", "operationId": "updatePageById", "tags": ["pages"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}, "responses": {"200": {"description": "Updated page", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page"}}}}}}, "delete": {"summary": "Delete a page by ID", "operationId": "deletePageById", "tags": ["pages"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "page deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "id": {"type": "string"}}, "required": ["message", "id"]}}}}}}}, "/api/pages/count": {"get": {"summary": "Count of pages", "operationId": "countPages", "tags": ["pages"], "responses": {"200": {"description": "Count of pages", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/posts": {"get": {"summary": "List all posts", "operationId": "findPosts", "tags": ["posts"], "parameters": [{"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100}, "description": "Limit number of documents returned"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1}, "description": "Page number for pagination"}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Field(s) to sort by"}, {"name": "where", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Advanced filtering (JSON string)"}], "responses": {"200": {"description": "A list of posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "totalDocs": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1}, "totalPages": {"type": "integer", "minimum": 0}, "page": {"type": "integer", "minimum": 1}, "pagingCounter": {"type": "integer", "minimum": 1}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "minimum": 1, "nullable": true}, "nextPage": {"type": "integer", "minimum": 1, "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage"]}}}}}}, "post": {"summary": "Create a new post", "operationId": "createPost", "tags": ["posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}, "responses": {"201": {"description": "Created post", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}}}, "patch": {"summary": "Update posts", "operationId": "updatePosts", "tags": ["posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}, "responses": {"200": {"description": "Updated posts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}}}, "delete": {"summary": "Delete posts", "operationId": "deletePosts", "tags": ["posts"], "responses": {"200": {"description": "posts deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "deletedCount": {"type": "integer"}}, "required": ["message"]}}}}}}}, "/api/posts/{id}": {"get": {"summary": "Retrieve a post by ID", "operationId": "findPostById", "tags": ["posts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}], "responses": {"200": {"description": "A single post", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}}}, "patch": {"summary": "Update a post by ID", "operationId": "updatePostById", "tags": ["posts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}, "responses": {"200": {"description": "Updated post", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}}}}, "delete": {"summary": "Delete a post by ID", "operationId": "deletePostById", "tags": ["posts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "post deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "id": {"type": "string"}}, "required": ["message", "id"]}}}}}}}, "/api/posts/count": {"get": {"summary": "Count of posts", "operationId": "countPosts", "tags": ["posts"], "responses": {"200": {"description": "Count of posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/media": {"get": {"summary": "List all media", "operationId": "findMedia", "tags": ["media"], "parameters": [{"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100}, "description": "Limit number of documents returned"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1}, "description": "Page number for pagination"}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Field(s) to sort by"}, {"name": "where", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Advanced filtering (JSON string)"}], "responses": {"200": {"description": "A list of media", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Media"}}, "totalDocs": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1}, "totalPages": {"type": "integer", "minimum": 0}, "page": {"type": "integer", "minimum": 1}, "pagingCounter": {"type": "integer", "minimum": 1}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "minimum": 1, "nullable": true}, "nextPage": {"type": "integer", "minimum": 1, "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage"]}}}}}}, "post": {"summary": "Create a new media", "operationId": "createMedia", "tags": ["media"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "_payload": {"type": "string", "description": "JSON-stringified object containing collection fields"}}, "required": ["file"]}}}}, "responses": {"201": {"description": "Created media", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Media"}}}}}}, "patch": {"summary": "Update media", "operationId": "updateMedia", "tags": ["media"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "_payload": {"type": "string", "description": "JSON-stringified object containing collection fields"}}}}}}, "responses": {"200": {"description": "Updated media", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Media"}}}}}}, "delete": {"summary": "Delete media", "operationId": "deleteMedia", "tags": ["media"], "responses": {"200": {"description": "media deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "deletedCount": {"type": "integer"}}, "required": ["message"]}}}}}}}, "/api/media/{id}": {"get": {"summary": "Retrieve a media by ID", "operationId": "findMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}], "responses": {"200": {"description": "A single media", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Media"}}}}}}, "patch": {"summary": "Update a media by ID", "operationId": "updateMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "_payload": {"type": "string", "description": "JSON-stringified object containing collection fields"}}}}}}, "responses": {"200": {"description": "Updated media", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Media"}}}}}}, "delete": {"summary": "Delete a media by ID", "operationId": "deleteMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "media deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "id": {"type": "string"}}, "required": ["message", "id"]}}}}}}}, "/api/media/count": {"get": {"summary": "Count of media", "operationId": "countMedia", "tags": ["media"], "responses": {"200": {"description": "Count of media", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/categories": {"get": {"summary": "List all categories", "operationId": "findCategories", "tags": ["categories"], "parameters": [{"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100}, "description": "Limit number of documents returned"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1}, "description": "Page number for pagination"}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Field(s) to sort by"}, {"name": "where", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Advanced filtering (JSON string)"}], "responses": {"200": {"description": "A list of categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "totalDocs": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1}, "totalPages": {"type": "integer", "minimum": 0}, "page": {"type": "integer", "minimum": 1}, "pagingCounter": {"type": "integer", "minimum": 1}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "minimum": 1, "nullable": true}, "nextPage": {"type": "integer", "minimum": 1, "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage"]}}}}}}, "post": {"summary": "Create a new category", "operationId": "createCategory", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}, "responses": {"201": {"description": "Created category", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}}}, "patch": {"summary": "Update categories", "operationId": "updateCategories", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}, "responses": {"200": {"description": "Updated categories", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}}}, "delete": {"summary": "Delete categories", "operationId": "deleteCategories", "tags": ["categories"], "responses": {"200": {"description": "categories deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "deletedCount": {"type": "integer"}}, "required": ["message"]}}}}}}}, "/api/categories/{id}": {"get": {"summary": "Retrieve a category by ID", "operationId": "findCategoryById", "tags": ["categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}], "responses": {"200": {"description": "A single category", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}}}, "patch": {"summary": "Update a category by ID", "operationId": "updateCategoryById", "tags": ["categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}, "responses": {"200": {"description": "Updated category", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}}}, "delete": {"summary": "Delete a category by ID", "operationId": "deleteCategoryById", "tags": ["categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "category deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "id": {"type": "string"}}, "required": ["message", "id"]}}}}}}}, "/api/categories/count": {"get": {"summary": "Count of categories", "operationId": "countCategories", "tags": ["categories"], "responses": {"200": {"description": "Count of categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/users": {"get": {"summary": "List all users", "operationId": "findUsers", "tags": ["users"], "parameters": [{"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100}, "description": "Limit number of documents returned"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1}, "description": "Page number for pagination"}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Field(s) to sort by"}, {"name": "where", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Advanced filtering (JSON string)"}], "responses": {"200": {"description": "A list of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "totalDocs": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1}, "totalPages": {"type": "integer", "minimum": 0}, "page": {"type": "integer", "minimum": 1}, "pagingCounter": {"type": "integer", "minimum": 1}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "minimum": 1, "nullable": true}, "nextPage": {"type": "integer", "minimum": 1, "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage"]}}}}}}, "post": {"summary": "Create a new user", "operationId": "createUser", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"201": {"description": "Created user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "patch": {"summary": "Update users", "operationId": "updateUsers", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"200": {"description": "Updated users", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "delete": {"summary": "Delete users", "operationId": "deleteUsers", "tags": ["users"], "responses": {"200": {"description": "users deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "deletedCount": {"type": "integer"}}, "required": ["message"]}}}}}}}, "/api/users/{id}": {"get": {"summary": "Retrieve a user by ID", "operationId": "findUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "depth", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 10}, "description": "Auto-populate relationships and uploads"}], "responses": {"200": {"description": "A single user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "patch": {"summary": "Update a user by ID", "operationId": "updateUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"200": {"description": "Updated user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "delete": {"summary": "Delete a user by ID", "operationId": "deleteUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "user deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "id": {"type": "string"}}, "required": ["message", "id"]}}}}}}}, "/api/users/count": {"get": {"summary": "Count of users", "operationId": "countUsers", "tags": ["users"], "responses": {"200": {"description": "Count of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/users/login": {"post": {"summary": "<PERSON><PERSON>", "operationId": "login", "tags": ["auth"], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/logout": {"post": {"summary": "Logout", "operationId": "logout", "tags": ["auth"], "responses": {"200": {"description": "Logout", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/unlock": {"post": {"summary": "Unlock", "operationId": "unlock", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Unlock", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/refresh-token": {"post": {"summary": "Refresh token", "operationId": "refreshToken", "tags": ["auth"], "responses": {"200": {"description": "Refresh token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/me": {"get": {"summary": "Current user", "operationId": "currentUser", "tags": ["auth"], "responses": {"200": {"description": "Current user", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/forgot-password": {"post": {"summary": "Forgot password", "operationId": "forgotPassword", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Forgot password", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/reset-password": {"post": {"summary": "Reset password", "operationId": "resetPassword", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "password": {"type": "string"}}}}}}, "responses": {"200": {"description": "Reset password", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/verify/{token}": {"post": {"summary": "Verify token", "operationId": "verifyToken", "tags": ["auth"], "responses": {"200": {"description": "Verify token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}}, "components": {"schemas": {"Page": {"type": "object", "properties": {"id": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "slug": {"type": "string", "nullable": true}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "_status": {"type": "string", "enum": ["draft", "published"], "nullable": true}}, "required": ["id", "updatedAt", "createdAt", "title"]}, "Post": {"type": "object", "properties": {"id": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "slug": {"type": "string", "nullable": true}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "_status": {"type": "string", "enum": ["draft", "published"], "nullable": true}}, "required": ["id", "updatedAt", "createdAt", "title"]}, "Media": {"type": "object", "properties": {"id": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "alt": {"type": "string", "nullable": true}, "filename": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "url": {"type": "string", "nullable": true}}, "required": ["id", "updatedAt", "createdAt"]}, "Category": {"type": "object", "properties": {"id": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "slug": {"type": "string", "nullable": true}}, "required": ["id", "updatedAt", "createdAt", "title"]}, "User": {"type": "object", "properties": {"id": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "format": "email"}, "roles": {"type": "array", "items": {"type": "string", "enum": ["subscriber", "editor", "admin", "super-admin"]}}}, "required": ["id", "updatedAt", "createdAt", "email", "roles"]}}}}